import { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Modal,
  Alert,
  RefreshControl,
  Linking,
} from 'react-native';
import { useAuth } from '../../hooks/useAuth';
import { FontAwesome, MaterialIcons } from '@expo/vector-icons';
import { firestore } from '../../config/firebase';
import { collection, getDocs, query, where, doc, getDoc, orderBy } from 'firebase/firestore';

interface Student {
  id: string;
  name: string;
  class: string;
  rollNumber: string;
  parentName: string;
  parentEmail: string;
  parentPhone: string;
  admissionDate: string;
  status: 'active' | 'inactive';
  address?: string;
}

export default function TeacherStudents() {
  console.log('TeacherStudents component loaded');
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [students, setStudents] = useState<Student[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [availableClasses, setAvailableClasses] = useState<string[]>([]);
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [showClassModal, setShowClassModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [showStudentModal, setShowStudentModal] = useState(false);
  const [teacherAssignedClass, setTeacherAssignedClass] = useState<string>('');

  // Fetch teacher profile and assigned class
  useEffect(() => {
    console.log('Initial useEffect running - fetching teacher profile and classes');
    fetchTeacherProfile();
    fetchAvailableClasses();
  }, []);

  // Filter students when search query or selected class changes
  useEffect(() => {
    console.log('Filter useEffect running due to changes in search, class, or students');
    console.log(`Current state - searchQuery: "${searchQuery}", selectedClass: "${selectedClass}", students: ${students.length}`);
    filterStudents();
  }, [searchQuery, selectedClass, students]);

  const fetchTeacherProfile = async () => {
    if (!user?.id) {
      Alert.alert('Error', 'User not authenticated');
      setLoading(false);
      return;
    }

    try {
      console.log('Fetching teacher profile...');
      const teachersQuery = query(collection(firestore, 'teachers'), where('userId', '==', user.id));
      const querySnapshot = await getDocs(teachersQuery);

      if (!querySnapshot.empty) {
        const teacherData = querySnapshot.docs[0].data();
        const assignedClass = teacherData.classTeacher || '';
        console.log('Teacher is assigned to class:', assignedClass);
        setTeacherAssignedClass(assignedClass);

        // Set the assigned class as the initially selected class if it exists
        if (assignedClass) {
          setSelectedClass(assignedClass);
        }
      } else {
        console.log('No teacher profile found');
      }

      // Fetch all students regardless of assigned class
      fetchStudents();
    } catch (error) {
      console.error('Error fetching teacher profile:', error);
      Alert.alert('Error', 'Failed to load teacher profile');
      setLoading(false);
    }
  };

  // Fetch all available classes from students collection
  const fetchAvailableClasses = async () => {
    try {
      console.log('Fetching available classes...');
      const studentsCollection = collection(firestore, 'students');
      const studentsQuery = query(studentsCollection, where('status', '==', 'active'));
      const querySnapshot = await getDocs(studentsQuery);

      // Extract unique class values
      const classSet = new Set<string>();
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.class) {
          classSet.add(data.class);
        }
      });

      const classes = Array.from(classSet).sort();
      console.log(`Found ${classes.length} unique classes:`, classes);

      if (classes.length === 0) {
        // Add dummy classes if none found
        const dummyClasses = ['9-A', '9-B', '10-A', '10-B'];
        setAvailableClasses(dummyClasses);
        console.log('No classes found, added dummy classes:', dummyClasses);
      } else {
        setAvailableClasses(classes);
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
      Alert.alert('Error', 'Failed to load classes');

      // Add dummy classes on error
      const dummyClasses = ['9-A', '9-B', '10-A', '10-B'];
      setAvailableClasses(dummyClasses);
    }
  };

  // Fetch students for a specific class or all students
  const fetchStudents = async () => {
    try {
      setLoading(true);
      console.log('Fetching students...');

      const studentsCollection = collection(firestore, 'students');
      let studentsQuery;

      if (selectedClass) {
        // If a class is selected, get students for that class
        console.log(`Fetching students for class: ${selectedClass}`);
        studentsQuery = query(
          studentsCollection,
          where('class', '==', selectedClass),
          where('status', '==', 'active')
        );
      } else {
        // Otherwise, get all active students
        console.log('Fetching all active students');
        studentsQuery = query(
          studentsCollection,
          where('status', '==', 'active')
        );
      }

      const querySnapshot = await getDocs(studentsQuery);
      console.log(`Found ${querySnapshot.size} students in Firestore`);

      const studentsData: Student[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        studentsData.push({
          id: doc.id,
          name: data.name || 'Unknown',
          class: data.class || 'Unknown',
          rollNumber: data.rollNumber || '',
          parentName: data.parentName || '',
          parentEmail: data.parentEmail || '',
          parentPhone: data.parentPhone || '',
          admissionDate: data.admissionDate || '',
          status: data.status || 'active',
          address: data.address || '',
        });
      });

      console.log(`Processed ${studentsData.length} student records`);

      // If no students found, add dummy data
      if (studentsData.length === 0) {
        console.log('No students found, adding dummy data');
        const dummyStudents: Student[] = [
          {
            id: '1',
            name: 'Rahul Sharma',
            class: '10-A',
            rollNumber: '1001',
            parentName: 'Vikram Sharma',
            parentEmail: '<EMAIL>',
            parentPhone: '+91 98765 43210',
            admissionDate: '2022-04-15',
            status: 'active',
            address: '123 Main St, Delhi',
          },
          {
            id: '2',
            name: 'Priya Patel',
            class: '9-B',
            rollNumber: '2002',
            parentName: 'Rajesh Patel',
            parentEmail: '<EMAIL>',
            parentPhone: '+91 98765 12345',
            admissionDate: '2022-06-10',
            status: 'active',
            address: '456 Park Ave, Mumbai',
          },
          {
            id: '3',
            name: 'Amit Kumar',
            class: '10-A',
            rollNumber: '1002',
            parentName: 'Suresh Kumar',
            parentEmail: '<EMAIL>',
            parentPhone: '+91 98765 67890',
            admissionDate: '2022-04-20',
            status: 'active',
            address: '789 School Rd, Bangalore',
          },
        ];

        // If a class is selected, filter the dummy data
        if (selectedClass) {
          const filteredDummyStudents = dummyStudents.filter(s => s.class === selectedClass);
          if (filteredDummyStudents.length > 0) {
            studentsData.push(...filteredDummyStudents);
          } else {
            // If no dummy students match the class, add at least one
            const newDummyStudent = {...dummyStudents[0], class: selectedClass};
            studentsData.push(newDummyStudent);
          }
        } else {
          // Add all dummy students if no class is selected
          studentsData.push(...dummyStudents);
        }

        console.log(`Added dummy students, now have ${studentsData.length} students`);
      }

      setStudents(studentsData);
    } catch (error) {
      console.error('Error fetching students:', error);
      Alert.alert('Error', 'Failed to load students');

      // Add dummy data on error
      const dummyStudents: Student[] = [
        {
          id: '1',
          name: 'Rahul Sharma',
          class: '10-A',
          rollNumber: '1001',
          parentName: 'Vikram Sharma',
          parentEmail: '<EMAIL>',
          parentPhone: '+91 98765 43210',
          admissionDate: '2022-04-15',
          status: 'active',
          address: '123 Main St, Delhi',
        },
        {
          id: '2',
          name: 'Priya Patel',
          class: '9-B',
          rollNumber: '2002',
          parentName: 'Rajesh Patel',
          parentEmail: '<EMAIL>',
          parentPhone: '+91 98765 12345',
          admissionDate: '2022-06-10',
          status: 'active',
          address: '456 Park Ave, Mumbai',
        },
      ];
      setStudents(dummyStudents);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Filter students based on search query and selected class
  const filterStudents = () => {
    console.log(`Filtering ${students.length} students...`);
    let filtered = [...students];

    // Filter by class if selected
    if (selectedClass) {
      console.log(`Filtering by class: ${selectedClass}`);
      filtered = filtered.filter(student => student.class === selectedClass);
      console.log(`After class filter: ${filtered.length} students remaining`);
    }

    // Filter by search query
    if (searchQuery) {
      console.log(`Filtering by search query: ${searchQuery}`);
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        student =>
          student.name.toLowerCase().includes(query) ||
          student.rollNumber.toLowerCase().includes(query) ||
          student.parentName.toLowerCase().includes(query) ||
          student.parentPhone.includes(query)
      );
      console.log(`After search filter: ${filtered.length} students remaining`);
    }

    console.log(`Setting filtered students: ${filtered.length} students`);
    setFilteredStudents(filtered);
  };

  // Handle refresh
  const onRefresh = () => {
    setRefreshing(true);
    fetchStudents();
  };

  // View student details
  const handleViewStudent = (student: Student) => {
    setSelectedStudent(student);
    setShowStudentModal(true);
  };

  // Direct call function
  const makePhoneCall = (phone: string) => {
    console.log('Attempting to call:', phone);
    // Make sure the phone number is properly formatted (remove spaces, etc.)
    const formattedPhone = phone.replace(/\s+/g, '');
    const phoneUrl = `tel:${formattedPhone}`;

    console.log('Opening URL:', phoneUrl);

    // Try to open the dialer directly
    Linking.openURL(phoneUrl).catch(err => {
      console.error('Error opening phone dialer:', err);
      Alert.alert('Error', 'Could not open phone dialer. Please try manually dialing the number.');
    });
  };

  // Call parent with confirmation
  const handleCallParent = (phone: string) => {
    Alert.alert(
      'Call Parent',
      `Would you like to call ${phone}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Call',
          onPress: () => makePhoneCall(phone)
        }
      ]
    );
  };

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="bg-white p-4 border-b border-gray-200">
        <View className="flex-row justify-between items-center">
          <Text className="text-xl font-bold text-gray-800">Student Directory</Text>
          <View className="h-10 w-10 bg-blue-100 rounded-full items-center justify-center">
            <FontAwesome name="graduation-cap" size={20} color="#3B82F6" />
          </View>
        </View>
      </View>

      {/* Search and Filter */}
      <View className="p-4 bg-white border-b border-gray-200">
        {/* Search Bar */}
        <View className="flex-row items-center bg-gray-100 px-4 py-3 rounded-xl mb-3">
          <FontAwesome name="search" size={18} color="#6B7280" />
          <TextInput
            className="flex-1 ml-2 text-gray-800"
            placeholder="Search by name, roll number, or parent..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9CA3AF"
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <FontAwesome name="times-circle" size={18} color="#6B7280" />
            </TouchableOpacity>
          ) : null}
        </View>

        {/* Class Filter */}
        <TouchableOpacity
          className="flex-row justify-between items-center bg-gray-100 px-4 py-3 rounded-xl"
          onPress={() => setShowClassModal(true)}
        >
          <View className="flex-row items-center">
            <FontAwesome name="filter" size={16} color="#6B7280" style={{ marginRight: 8 }} />
            <Text className="text-gray-800">
              {selectedClass ? `Class: ${selectedClass}` : 'Filter by Class'}
            </Text>
          </View>
          {selectedClass ? (
            <TouchableOpacity
              onPress={(e) => {
                e.stopPropagation();
                setSelectedClass('');
              }}
              className="bg-gray-200 rounded-full p-1"
            >
              <FontAwesome name="times" size={14} color="#6B7280" />
            </TouchableOpacity>
          ) : (
            <FontAwesome name="chevron-down" size={14} color="#6B7280" />
          )}
        </TouchableOpacity>
      </View>

      {/* Student List */}
      {loading ? (
        <View className="flex-1 items-center justify-center p-10">
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text className="mt-4 text-gray-600">Loading students...</Text>
        </View>
      ) : (
        <ScrollView
          className="flex-1"
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          <View className="p-4">
            {/* Results Count */}
            <Text className="text-gray-500 mb-3">
              {filteredStudents.length} {filteredStudents.length === 1 ? 'student' : 'students'} found
            </Text>

            {filteredStudents.length === 0 ? (
              <View className="items-center justify-center py-10 bg-white rounded-xl p-4 shadow-sm">
                <FontAwesome name="users" size={40} color="#D1D5DB" />
                <Text className="text-gray-500 mt-4 text-center">No students found</Text>
                <Text className="text-gray-400 text-sm text-center mt-1">
                  Try adjusting your search or filters
                </Text>
              </View>
            ) : (
              <View className="space-y-3">
                {filteredStudents.map((student) => (
                  <View
                    key={student.id}
                    className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex-row items-center"
                  >
                    <TouchableOpacity
                      className="flex-1 flex-row items-center"
                      onPress={() => handleViewStudent(student)}
                      activeOpacity={0.7}
                    >
                      <View className="w-12 h-12 bg-blue-100 rounded-full items-center justify-center mr-3">
                        <Text className="text-blue-600 font-bold">
                          {student.name.charAt(0).toUpperCase()}
                        </Text>
                      </View>
                      <View className="flex-1">
                        <Text className="font-bold text-gray-800">{student.name}</Text>
                        <View className="flex-row items-center">
                          <Text className="text-gray-500 mr-2">Class {student.class}</Text>
                          <Text className="text-gray-500">Roll: {student.rollNumber}</Text>
                        </View>
                      </View>
                      <FontAwesome name="chevron-right" size={16} color="#9CA3AF" />
                    </TouchableOpacity>

                    {student.parentPhone && (
                      <TouchableOpacity
                        className="bg-green-100 p-2 rounded-full ml-2"
                        onPress={() => makePhoneCall(student.parentPhone)}
                      >
                        <FontAwesome name="phone" size={16} color="#10B981" />
                      </TouchableOpacity>
                    )}
                  </View>
                ))}
              </View>
            )}
          </View>
        </ScrollView>
      )}

      {/* Class Selection Modal */}
      <Modal
        visible={showClassModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowClassModal(false)}
      >
        <View className="flex-1 bg-black/50 justify-center">
          <View className="bg-white mx-4 rounded-xl overflow-hidden">
            <View className="p-4 border-b border-gray-200">
              <Text className="text-xl font-bold text-gray-800">Select Class</Text>
            </View>

            <ScrollView style={{ maxHeight: 300 }}>
              {availableClasses.map((classItem) => (
                <TouchableOpacity
                  key={classItem}
                  className="p-4 border-b border-gray-100 flex-row justify-between items-center"
                  onPress={() => {
                    setSelectedClass(classItem);
                    setShowClassModal(false);
                    fetchStudents();
                  }}
                >
                  <View className="flex-row items-center">
                    <View
                      className={`w-8 h-8 rounded-full items-center justify-center mr-3 ${
                        classItem === teacherAssignedClass ? 'bg-blue-100' : 'bg-gray-100'
                      }`}
                    >
                      <Text
                        className={`font-medium ${
                          classItem === teacherAssignedClass ? 'text-blue-600' : 'text-gray-600'
                        }`}
                      >
                        {classItem.split('-')[0]}
                      </Text>
                    </View>
                    <Text
                      className={`text-base ${
                        classItem === teacherAssignedClass ? 'font-bold text-blue-600' : 'text-gray-800'
                      }`}
                    >
                      {classItem}
                      {classItem === teacherAssignedClass && ' (Your Class)'}
                    </Text>
                  </View>
                  {classItem === selectedClass && (
                    <FontAwesome name="check" size={16} color="#3B82F6" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>

            <View className="p-4 border-t border-gray-200">
              <TouchableOpacity
                className="p-4 bg-gray-200 rounded-lg"
                onPress={() => setShowClassModal(false)}
              >
                <Text className="text-center font-semibold text-gray-800">Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Student Details Modal */}
      <Modal
        visible={showStudentModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowStudentModal(false)}
      >
        <View className="flex-1 bg-black/50 justify-end">
          <View className="bg-white rounded-t-3xl max-h-[90%]">
            <View className="p-4 border-b border-gray-200 flex-row justify-between items-center">
              <Text className="text-xl font-bold text-gray-800">Student Details</Text>
              <TouchableOpacity
                onPress={() => setShowStudentModal(false)}
                className="p-2"
              >
                <FontAwesome name="close" size={20} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <ScrollView className="p-4" contentContainerStyle={{ paddingBottom: 30 }}>
              {selectedStudent ? (
                <View className="space-y-6">
                  {/* Student Basic Info */}
                  <View className="items-center mb-2">
                    <View className="w-20 h-20 bg-blue-100 rounded-full items-center justify-center mb-3">
                      <FontAwesome name="user" size={32} color="#3B82F6" />
                    </View>
                    <Text className="text-xl font-bold text-gray-800">{selectedStudent.name}</Text>
                    <View className="flex-row items-center mt-1">
                      <Text className="text-gray-600">Class {selectedStudent.class}</Text>
                      {selectedStudent.status && (
                        <View className={`ml-2 px-2 py-0.5 rounded-full ${selectedStudent.status === 'active' ? 'bg-green-100' : 'bg-red-100'}`}>
                          <Text className={`text-xs font-medium ${selectedStudent.status === 'active' ? 'text-green-800' : 'text-red-800'}`}>
                            {selectedStudent.status}
                          </Text>
                        </View>
                      )}
                    </View>
                  </View>

                  {/* Academic Information */}
                  <View className="bg-gray-50 p-4 rounded-xl">
                    <Text className="text-base font-bold text-gray-800 mb-3">Academic Information</Text>

                    <View className="space-y-3">
                      <View className="flex-row">
                        <Text className="text-gray-500 w-32">Roll Number:</Text>
                        <Text className="text-gray-800 font-medium flex-1">{selectedStudent.rollNumber || 'Not assigned'}</Text>
                      </View>

                      <View className="flex-row">
                        <Text className="text-gray-500 w-32">Class:</Text>
                        <Text className="text-gray-800 font-medium flex-1">{selectedStudent.class}</Text>
                      </View>

                      <View className="flex-row">
                        <Text className="text-gray-500 w-32">Admission Date:</Text>
                        <Text className="text-gray-800 font-medium flex-1">
                          {selectedStudent.admissionDate ? new Date(selectedStudent.admissionDate).toLocaleDateString() : 'Not available'}
                        </Text>
                      </View>
                    </View>
                  </View>

                  {/* Contact Information */}
                  <View className="bg-gray-50 p-4 rounded-xl">
                    <Text className="text-base font-bold text-gray-800 mb-3">Parent Information</Text>

                    <View className="space-y-3">
                      <View className="flex-row">
                        <Text className="text-gray-500 w-32">Parent Name:</Text>
                        <Text className="text-gray-800 font-medium flex-1">{selectedStudent.parentName || 'Not available'}</Text>
                      </View>

                      <View className="flex-row items-center">
                        <Text className="text-gray-500 w-32">Phone Number:</Text>
                        <Text className="text-gray-800 font-medium flex-1">{selectedStudent.parentPhone || 'Not available'}</Text>
                        {selectedStudent.parentPhone && (
                          <TouchableOpacity
                            className="bg-green-100 p-2 rounded-full"
                            onPress={() => makePhoneCall(selectedStudent.parentPhone)}
                          >
                            <FontAwesome name="phone" size={16} color="#10B981" />
                          </TouchableOpacity>
                        )}
                      </View>

                      <View className="flex-row">
                        <Text className="text-gray-500 w-32">Email:</Text>
                        <Text className="text-gray-800 font-medium flex-1">{selectedStudent.parentEmail || 'Not available'}</Text>
                      </View>

                      {selectedStudent.address && (
                        <View className="flex-row">
                          <Text className="text-gray-500 w-32">Address:</Text>
                          <Text className="text-gray-800 font-medium flex-1">{selectedStudent.address}</Text>
                        </View>
                      )}
                    </View>
                  </View>

                  {/* Emergency Actions */}
                  <View className="bg-red-50 p-4 rounded-xl">
                    <Text className="text-base font-bold text-red-800 mb-3">Emergency Contact</Text>

                    <TouchableOpacity
                      className="bg-red-500 p-4 rounded-xl"
                      onPress={() => {
                        if (selectedStudent.parentPhone) {
                          makePhoneCall(selectedStudent.parentPhone);
                        } else {
                          Alert.alert('Error', 'No parent phone number available');
                        }
                      }}
                    >
                      <View className="flex-row justify-center items-center">
                        <FontAwesome name="phone" size={16} color="white" style={{ marginRight: 8 }} />
                        <Text className="text-white font-bold text-center">Call Parent</Text>
                      </View>
                    </TouchableOpacity>
                  </View>

                  {/* Close Button */}
                  <TouchableOpacity
                    className="bg-blue-500 p-4 rounded-xl mt-4"
                    onPress={() => setShowStudentModal(false)}
                  >
                    <Text className="text-white font-bold text-center">Close</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View className="items-center justify-center py-10">
                  <FontAwesome name="exclamation-circle" size={40} color="#9CA3AF" />
                  <Text className="text-gray-500 mt-4 text-center">Student information not available</Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}
