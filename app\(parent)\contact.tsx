import { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Linking, Alert, ActivityIndicator, TextInput } from 'react-native';
import { FontAwesome, MaterialIcons } from '@expo/vector-icons';
import { useAuth } from '../../hooks/useAuth';
import { firestore } from '../../config/firebase';
import { collection, getDocs, query, where } from 'firebase/firestore';

interface ContactInfo {
  id: string;
  name: string;
  role: string;
  phone: string;
  email?: string;
}

export default function ContactScreen() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [contacts, setContacts] = useState<ContactInfo[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredContacts, setFilteredContacts] = useState<ContactInfo[]>([]);

  useEffect(() => {
    fetchContacts();
  }, []);

  // Filter contacts based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredContacts(contacts);
      return;
    }

    const query = searchQuery.toLowerCase().trim();
    const filtered = contacts.filter(contact =>
      // Only search through teachers (not administrative contacts)
      contact.role.toLowerCase().includes('teacher') &&
      contact.name.toLowerCase().includes(query)
    );

    setFilteredContacts(filtered);
  }, [searchQuery, contacts]);

  const fetchContacts = async () => {
    try {
      setLoading(true);
      console.log('Fetching contact information...');

      // Fetch teachers from Firestore
      const teachersCollection = collection(firestore, 'teachers');
      const teachersSnapshot = await getDocs(teachersCollection);

      const contactsData: ContactInfo[] = [];

      // Process teacher data
      teachersSnapshot.forEach((doc) => {
        const data = doc.data();
        // Only add teachers with contact information
        if (data.phone) {
          contactsData.push({
            id: doc.id,
            name: data.name || 'Unknown',
            role: data.designation || 'Teacher',
            phone: data.phone || '',
            email: data.email || '',
          });
        }
      });

      // Fetch school contacts from Firestore
      const schoolContactsCollection = collection(firestore, 'schoolContacts');
      const schoolContactsSnapshot = await getDocs(schoolContactsCollection);

      // Process school contact data
      schoolContactsSnapshot.forEach((doc) => {
        const data = doc.data();
        contactsData.push({
          id: doc.id,
          name: data.name || 'School Office',
          role: data.role || 'Administration',
          phone: data.phone || '',
          email: data.email || '',
        });
      });

      console.log(`Found ${contactsData.length} contacts`);

      // If no contacts found, show a message instead of dummy data
      if (contactsData.length === 0) {
        console.log('No contact information found in the database');
      }

      console.log(`Found ${contactsData.length} contacts`);
      setContacts(contactsData);
      setFilteredContacts(contactsData);
    } catch (error) {
      console.error('Error fetching contacts:', error);
      Alert.alert('Error', 'Failed to load contact information. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCall = (phone: string, name: string) => {
    Alert.alert(
      'Make a Call',
      `Would you like to call ${name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Call', onPress: () => Linking.openURL(`tel:${phone}`) }
      ]
    );
  };

  const handleEmail = (email: string, name: string) => {
    if (!email) return;

    Alert.alert(
      'Send Email',
      `Would you like to email ${name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Email', onPress: () => Linking.openURL(`mailto:${email}`) }
      ]
    );
  };

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="bg-white px-4 py-4 border-b border-gray-200">
        <Text className="text-xl font-bold text-gray-800">Contact Information</Text>
        <Text className="text-sm text-gray-500 mt-1">
          Contact school staff for any queries
        </Text>
      </View>

      {/* Enhanced Mobile-Friendly Teacher Search Bar */}
      <View className="bg-white px-4 py-3 border-b border-gray-200 shadow-sm">
        {/* Search Input with Floating Label Effect */}
        <View className="mb-1">
          <Text className="text-xs font-medium text-blue-600 mb-1 ml-1">
            FIND TEACHERS
          </Text>
          <View className="flex-row items-center bg-gray-50 px-4 py-3 rounded-xl border border-gray-200">
            <View className="w-8 h-8 bg-blue-100 rounded-full items-center justify-center mr-3">
              <FontAwesome name="search" size={16} color="#3B82F6" />
            </View>
            <TextInput
              className="flex-1 text-gray-800 text-base font-medium"
              placeholder="Search by teacher name..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#9CA3AF"
              autoCapitalize="none"
              autoCorrect={false}
              returnKeyType="search"
              clearButtonMode="while-editing"
            />
            {searchQuery ? (
              <TouchableOpacity
                onPress={() => setSearchQuery('')}
                className="bg-gray-200 w-7 h-7 rounded-full items-center justify-center"
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <FontAwesome name="times" size={14} color="#4B5563" />
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        {/* Search Status Indicator */}
        {searchQuery && (
          <View className="flex-row items-center justify-between mt-2 px-1">
            <View className="flex-row items-center">
              <Text className="text-xs text-gray-500">
                {filteredContacts.length > 0
                  ? `Found ${filteredContacts.length} ${filteredContacts.length === 1 ? 'teacher' : 'teachers'}`
                  : 'No teachers found'}
              </Text>
            </View>
            {filteredContacts.length === 0 && (
              <TouchableOpacity
                onPress={() => setSearchQuery('')}
                className="flex-row items-center"
              >
                <Text className="text-xs text-blue-600 font-medium">Clear search</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>

      <ScrollView className="flex-1 p-4">
        {loading ? (
          <View className="items-center justify-center p-8">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="text-gray-500 mt-4">Loading contacts...</Text>
          </View>
        ) : contacts.length === 0 ? (
          <View className="items-center justify-center p-8 bg-white rounded-xl shadow-sm border border-gray-100">
            <FontAwesome name="address-book-o" size={40} color="#9CA3AF" />
            <Text className="text-gray-500 mt-4 text-center">No contact information available.</Text>
            <Text className="text-gray-400 mt-2 text-center">Contact information will be added by the administrator.</Text>
          </View>
        ) : (
          <>
            {/* Teacher Search Results - Only shown when searching */}
            {searchQuery ? (
              <View>
                <View className="flex-row items-center justify-between mb-4">
                  <View className="flex-row items-center">
                    <View className="w-9 h-9 bg-blue-100 rounded-full items-center justify-center mr-3">
                      <FontAwesome name="graduation-cap" size={16} color="#3B82F6" />
                    </View>
                    <View>
                      <Text className="text-gray-800 font-bold text-base">Teacher Directory</Text>
                      <Text className="text-xs text-gray-500">Search results for "{searchQuery}"</Text>
                    </View>
                  </View>
                </View>

                {filteredContacts.length > 0 ? (
                  filteredContacts.map((contact) => (
                    <View
                      key={contact.id}
                      className="bg-white p-4 rounded-xl shadow-sm mb-4 border-l-4 border border-blue-100 border-l-blue-400"
                      style={{ elevation: 1 }}
                    >
                      <View className="flex-row items-center mb-3">
                        <View className="w-14 h-14 bg-blue-50 rounded-full items-center justify-center mr-3 border border-blue-100">
                          <FontAwesome name="user" size={24} color="#3B82F6" />
                        </View>
                        <View className="flex-1">
                          <Text className="text-lg font-bold text-gray-800">{contact.name}</Text>
                          <View className="bg-blue-50 px-2 py-0.5 rounded-full self-start mt-1">
                            <Text className="text-blue-700 text-xs">{contact.role}</Text>
                          </View>
                        </View>
                      </View>

                      <View className="border-t border-gray-100 pt-3 mt-1">
                        {/* Contact Actions */}
                        <View className="flex-row justify-between">
                          {/* Call Button */}
                          <TouchableOpacity
                            className="flex-1 flex-row items-center justify-center bg-green-50 py-3 rounded-xl mr-2 border border-green-100"
                            onPress={() => handleCall(contact.phone, contact.name)}
                          >
                            <FontAwesome name="phone" size={16} color="#10B981" style={{ marginRight: 8 }} />
                            <Text className="text-green-700 font-bold">Call</Text>
                          </TouchableOpacity>

                          {/* Email Button - Only if email is available */}
                          {contact.email ? (
                            <TouchableOpacity
                              className="flex-1 flex-row items-center justify-center bg-blue-50 py-3 rounded-xl ml-2 border border-blue-100"
                              onPress={() => handleEmail(contact.email!, contact.name)}
                            >
                              <MaterialIcons name="email" size={16} color="#3B82F6" style={{ marginRight: 8 }} />
                              <Text className="text-blue-700 font-bold">Email</Text>
                            </TouchableOpacity>
                          ) : (
                            <View className="flex-1 ml-2" />
                          )}
                        </View>

                        {/* Contact Details */}
                        <View className="mt-3 bg-gray-50 p-3 rounded-lg">
                          <View className="flex-row items-center mb-2">
                            <FontAwesome name="phone" size={14} color="#6B7280" style={{ marginRight: 8, width: 16 }} />
                            <Text className="text-gray-700">{contact.phone}</Text>
                          </View>

                          {contact.email && (
                            <View className="flex-row items-center">
                              <MaterialIcons name="email" size={14} color="#6B7280" style={{ marginRight: 8, width: 16 }} />
                              <Text className="text-gray-700">{contact.email}</Text>
                            </View>
                          )}
                        </View>
                      </View>
                    </View>
                  ))
                ) : (
                  <View className="bg-gray-50 p-6 rounded-xl border border-gray-200 items-center justify-center">
                    <View className="w-16 h-16 bg-gray-200 rounded-full items-center justify-center mb-4">
                      <FontAwesome name="search" size={24} color="#9CA3AF" />
                    </View>
                    <Text className="text-gray-800 font-bold text-lg mb-1 text-center">No teachers found</Text>
                    <Text className="text-gray-500 text-center mb-4">
                      We couldn't find any teachers matching "{searchQuery}"
                    </Text>
                    <TouchableOpacity
                      className="bg-blue-500 px-6 py-2.5 rounded-lg"
                      onPress={() => setSearchQuery('')}
                    >
                      <Text className="text-white font-bold">Clear Search</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            ) : null}

            {/* All Contacts - Only shown when not searching */}
            {!searchQuery && (
              <>
                {/* Contact Cards */}
                {contacts.map((contact) => (
                  <View
                    key={contact.id}
                    className="bg-white p-4 rounded-xl shadow-sm mb-4 border border-gray-100"
                  >
                    <View className="flex-row items-center mb-3">
                      <View className="w-12 h-12 bg-purple-100 rounded-full items-center justify-center mr-3">
                        <FontAwesome
                          name={contact.role.toLowerCase().includes('teacher') ? 'user' :
                                contact.role.toLowerCase().includes('principal') ? 'user-circle' : 'building'}
                          size={20}
                          color="#8B5CF6"
                        />
                      </View>
                      <View className="flex-1">
                        <Text className="text-lg font-bold text-gray-800">{contact.name}</Text>
                        <Text className="text-gray-600">{contact.role}</Text>
                      </View>
                    </View>

                    <View className="border-t border-gray-100 pt-3">
                      {/* Phone */}
                      <TouchableOpacity
                        className="flex-row items-center mb-2"
                        onPress={() => handleCall(contact.phone, contact.name)}
                      >
                        <View className="w-8 h-8 bg-green-100 rounded-full items-center justify-center mr-3">
                          <FontAwesome name="phone" size={14} color="#10B981" />
                        </View>
                        <Text className="text-gray-800">{contact.phone}</Text>
                      </TouchableOpacity>

                      {/* Email (if available) */}
                      {contact.email && (
                        <TouchableOpacity
                          className="flex-row items-center"
                          onPress={() => handleEmail(contact.email!, contact.name)}
                        >
                          <View className="w-8 h-8 bg-blue-100 rounded-full items-center justify-center mr-3">
                            <MaterialIcons name="email" size={14} color="#3B82F6" />
                          </View>
                          <Text className="text-gray-800">{contact.email}</Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                ))}

                {/* Emergency Contact */}
                <View className="bg-red-50 p-4 rounded-xl shadow-sm mb-4 border border-red-100">
                  <View className="flex-row items-center mb-3">
                    <View className="w-12 h-12 bg-red-100 rounded-full items-center justify-center mr-3">
                      <FontAwesome name="ambulance" size={20} color="#EF4444" />
                    </View>
                    <View className="flex-1">
                      <Text className="text-lg font-bold text-gray-800">Emergency Contact</Text>
                      <Text className="text-gray-600">For urgent matters only</Text>
                    </View>
                  </View>

                  <View className="border-t border-red-100 pt-3">
                    <TouchableOpacity
                      className="flex-row items-center"
                      onPress={() => handleCall('+91 112', 'Emergency Services')}
                    >
                      <View className="w-8 h-8 bg-red-100 rounded-full items-center justify-center mr-3">
                        <FontAwesome name="phone" size={14} color="#EF4444" />
                      </View>
                      <Text className="text-gray-800">+91 112</Text>
                    </TouchableOpacity>
                  </View>
                </View>

                {/* Help Text */}
                <View className="bg-blue-50 p-4 rounded-xl mb-6">
                  <Text className="text-gray-700 text-center">
                    Please contact the school office during working hours (8:00 AM - 4:00 PM) for general inquiries.
                  </Text>
                </View>
              </>
            )}
          </>
        )}
      </ScrollView>
    </View>
  );
}
