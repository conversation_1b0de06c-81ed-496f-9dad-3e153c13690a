import { useState, useEffect, FC } from 'react';
import { router } from 'expo-router';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  Image,
  RefreshControl,
  ActivityIndicator,
  // useWindowDimensions,
  TextInput,
  Platform,
  Linking,
} from 'react-native';
import { useAuth } from '../../../hooks/useAuth';
import { FontAwesome } from '@expo/vector-icons';
import { format } from 'date-fns';
import { firestore, storage } from '../../../config/firebase';
import { collection, getDocs, doc, updateDoc, query, orderBy, where, getDoc, setDoc } from 'firebase/firestore';
import { ref as storageRef, getDownloadURL } from 'firebase/storage';
import * as ImagePicker from 'expo-image-picker';

interface Payment {
  id: string;
  studentId: string;
  studentName: string;
  studentClass: string;
  parentId: string;
  parentName: string;
  class: string; // For backward compatibility
  amount: number;
  upiReferenceId: string;
  screenshot: string;
  date: string;
  status: 'pending' | 'approved' | 'rejected';
  mobileNumber?: string;
  notes?: string;
  approvedBy?: string;
  approvedAt?: string;
  rejectedBy?: string;
  rejectedAt?: string;
  createdAt?: string;
  isRealtimeDatabase?: boolean;
}

// Component to display images from Realtime Database
interface RealtimeDatabaseImageProps {
  url: string;
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
}

const RealtimeDatabaseImage: FC<RealtimeDatabaseImageProps> = ({ url, onLoadStart, onLoadEnd }) => {
  const [imageData, setImageData] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchImageData = async () => {
      if (onLoadStart) onLoadStart();
      setLoading(true);

      try {
        // Fetch the JSON data from the Realtime Database
        const response = await fetch(url);
        const data = await response.json();

        if (data && data.base64Data) {
          // Create a data URL from the base64 data
          const dataUrl = `data:${data.contentType || 'image/jpeg'};base64,${data.base64Data}`;
          setImageData(dataUrl);
          setError(null);
        } else {
          setError('Invalid image data');
        }
      } catch (err) {
        console.error('Error fetching image from Realtime Database:', err);
        setError('Failed to load image');
      } finally {
        setLoading(false);
        if (onLoadEnd) onLoadEnd();
      }
    };

    fetchImageData();
  }, [url, onLoadStart, onLoadEnd]);

  if (loading) {
    return (
      <View className="w-full h-48 rounded-xl bg-gray-100 items-center justify-center">
        <ActivityIndicator size="large" color="#3B82F6" />
      </View>
    );
  }

  if (error || !imageData) {
    return (
      <View className="w-full h-48 rounded-xl bg-gray-100 items-center justify-center">
        <Text className="text-red-500">{error || 'No image data'}</Text>
      </View>
    );
  }

  return (
    <Image
      source={{ uri: imageData }}
      className="w-full h-48 rounded-xl bg-gray-100"
      resizeMode="contain"
    />
  );
};

interface PaymentSettings {
  upiId: string;
  qrCodeUrl: string;
  lastUpdated: string;
}

export default function AdminPayments() {
  // const { width } = useWindowDimensions(); // Uncomment if needed for responsive design
  const { user } = useAuth();
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [filteredPayments, setFilteredPayments] = useState<Payment[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string>('');
  const [adminNote, setAdminNote] = useState<string>('');
  const [showNoteModal, setShowNoteModal] = useState(false);
  const [noteAction, setNoteAction] = useState<'approve' | 'reject'>('approve');

  // Payment settings state
  const [paymentSettings, setPaymentSettings] = useState<PaymentSettings>({
    upiId: 'school@ybl',
    qrCodeUrl: 'https://upload.wikimedia.org/wikipedia/commons/d/d0/QR_code_for_mobile_English_Wikipedia.svg',
    lastUpdated: new Date().toISOString()
  });
  const [newUpiId, setNewUpiId] = useState('');
  const [newQrCode, setNewQrCode] = useState<string | null>(null);
  const [qrCodeBase64, setQrCodeBase64] = useState<string | null>(null);

  // Fetch payments and settings from Firestore
  useEffect(() => {
    fetchPayments();
    fetchPaymentSettings();
  }, []);

  // Filter payments when filter criteria change
  useEffect(() => {
    filterPayments();
  }, [payments, statusFilter, searchQuery]);

  const fetchPayments = async () => {
    setIsLoading(true);
    try {
      const paymentsCollection = collection(firestore, 'payments');
      const paymentsQuery = query(paymentsCollection, orderBy('date', 'desc'));
      const querySnapshot = await getDocs(paymentsQuery);

      const paymentsData: Payment[] = [];

      for (const doc of querySnapshot.docs) {
        const data = doc.data();

        // Get student and parent details directly from the payment document
        // This is more efficient and ensures we use the data that was saved at payment time
        let studentName = data.studentName || 'Unknown Student';
        let parentName = data.parentName || 'Unknown Parent';
        let className = data.studentClass || 'Unknown Class';

        console.log(`Payment ${doc.id} - Student: ${studentName}, Class: ${className}, Parent: ${parentName}`);

        // Get screenshot URL
        let screenshotUrl = '';
        try {
          // Check if we have screenshot data

          if (data.screenshotUrl) {
            // Use the URL directly if available
            screenshotUrl = data.screenshotUrl;
            console.log('Using existing screenshot URL:', screenshotUrl);
          } else if (data.screenshotPath && !data.uploadFailed) {
            // For Firebase Storage, get the download URL
            try {
              // Create a reference to the specific storage bucket
              const imageRef = storageRef(storage, data.screenshotPath);
              screenshotUrl = await getDownloadURL(imageRef);
              console.log('Got download URL from Firebase Storage:', screenshotUrl);
            } catch (error) {
              console.error('Error getting download URL from Firebase Storage:', error);
              // Try a direct URL format as fallback
              try {
                const directUrl = `https://firebasestorage.googleapis.com/v0/b/test-7e932.appspot.com/o/${encodeURIComponent(data.screenshotPath)}?alt=media`;
                console.log('Trying direct URL:', directUrl);
                screenshotUrl = directUrl;
              } catch (directError) {
                console.error('Direct URL also failed:', directError);
                screenshotUrl = 'https://via.placeholder.com/400x300?text=Image+Not+Available';
              }
            }
          }
        } catch (error) {
          console.error('Error getting screenshot URL:', error);
          screenshotUrl = 'https://via.placeholder.com/400x300?text=No+Screenshot';
        }

        // Get mobile number if available
        let mobileNumber = '';
        if (data.parentId) {
          try {
            const userDoc = await getDocs(query(
              collection(firestore, 'users'),
              where('id', '==', data.parentId)
            ));

            if (!userDoc.empty) {
              mobileNumber = userDoc.docs[0].data().phone || '';
            }
          } catch (error) {
            console.error('Error fetching mobile number:', error);
          }
        }

        paymentsData.push({
          id: doc.id,
          studentId: data.studentId || '',
          studentName,
          studentClass: data.studentClass || className,
          parentId: data.parentId || '',
          parentName,
          class: className, // For backward compatibility
          amount: data.amount || 0,
          upiReferenceId: data.upiReferenceId || 'N/A',
          screenshot: screenshotUrl,
          date: data.date || new Date().toISOString().split('T')[0],
          status: data.status || 'pending',
          mobileNumber,
          notes: data.notes || '',
          approvedBy: data.approvedBy,
          approvedAt: data.approvedAt,
          rejectedBy: data.rejectedBy,
          rejectedAt: data.rejectedAt,
          createdAt: data.createdAt || new Date().toISOString(),
        });
      }

      setPayments(paymentsData);
      setFilteredPayments(paymentsData);
    } catch (error) {
      console.error('Error fetching payments:', error);
      Alert.alert('Error', 'Failed to load payments. Please try again.');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  // Filter payments based on status and search query
  const filterPayments = () => {
    if (!payments.length) return;

    let filtered = [...payments];

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(payment => payment.status === statusFilter);
    }

    // Apply search query filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        payment =>
          payment.studentName.toLowerCase().includes(query) ||
          payment.parentName.toLowerCase().includes(query) ||
          payment.class.toLowerCase().includes(query) ||
          payment.upiReferenceId.toLowerCase().includes(query) ||
          payment.amount.toString().includes(query)
      );
    }

    setFilteredPayments(filtered);
  };

  const handleApprovePayment = (payment: Payment) => {
    // Reset admin note when starting approval process
    setAdminNote('');
    setNoteAction('approve');
    setSelectedPayment(payment);
    setShowNoteModal(true);
  };

  const confirmApprovePayment = async (payment: Payment) => {
    setIsLoading(true);
    try {
      // Update payment status in Firestore
      const paymentRef = doc(firestore, 'payments', payment.id);
      await updateDoc(paymentRef, {
        status: 'approved',
        approvedBy: user?.id,
        approvedAt: new Date().toISOString(),
        notes: adminNote || ''
      });

      // Update local state
      const updatedPayment: Payment = {
        ...payment,
        status: 'approved',
        approvedBy: user?.id,
        approvedAt: new Date().toISOString(),
        notes: adminNote || ''
      };

      setPayments(
        payments.map((p) =>
          p.id === payment.id ? updatedPayment : p
        )
      );

      // Update filtered payments too
      setFilteredPayments(
        filteredPayments.map((p) =>
          p.id === payment.id ? updatedPayment : p
        )
      );

      Alert.alert('Success', 'Payment approved successfully');
      setShowModal(false);
      setShowNoteModal(false);
    } catch (error) {
      console.error('Error approving payment:', error);
      Alert.alert('Error', 'Failed to approve payment');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRejectPayment = (payment: Payment) => {
    // Reset admin note when starting rejection process
    setAdminNote('');
    setNoteAction('reject');
    setSelectedPayment(payment);
    setShowNoteModal(true);
  };

  const confirmRejectPayment = async (payment: Payment) => {
    setIsLoading(true);
    try {
      // Update payment status in Firestore
      const paymentRef = doc(firestore, 'payments', payment.id);
      await updateDoc(paymentRef, {
        status: 'rejected',
        rejectedBy: user?.id,
        rejectedAt: new Date().toISOString(),
        notes: adminNote || ''
      });

      // Update local state
      const updatedPayment: Payment = {
        ...payment,
        status: 'rejected',
        rejectedBy: user?.id,
        rejectedAt: new Date().toISOString(),
        notes: adminNote || ''
      };

      setPayments(
        payments.map((p) =>
          p.id === payment.id ? updatedPayment : p
        )
      );

      // Update filtered payments too
      setFilteredPayments(
        filteredPayments.map((p) =>
          p.id === payment.id ? updatedPayment : p
        )
      );

      Alert.alert('Success', 'Payment rejected successfully');
      setShowModal(false);
      setShowNoteModal(false);
    } catch (error) {
      console.error('Error rejecting payment:', error);
      Alert.alert('Error', 'Failed to reject payment');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const fetchPaymentSettings = async () => {
    try {
      // Use Firestore for payment settings
      const paymentSettingsDoc = doc(firestore, 'settings', 'payments');
      const snapshot = await getDoc(paymentSettingsDoc);

      if (snapshot.exists()) {
        const data = snapshot.data() as PaymentSettings;
        setPaymentSettings(data);
      } else {
        // Create default settings if they don't exist
        const defaultSettings: PaymentSettings = {
          upiId: 'school@ybl',
          qrCodeUrl: 'https://upload.wikimedia.org/wikipedia/commons/d/d0/QR_code_for_mobile_English_Wikipedia.svg',
          lastUpdated: new Date().toISOString()
        };

        try {
          // Save to Firestore
          await setDoc(paymentSettingsDoc, defaultSettings);
          setPaymentSettings(defaultSettings);
        } catch (saveError) {
          console.error('Error saving default payment settings:', saveError);
          // Still use the default settings even if save fails
          setPaymentSettings(defaultSettings);
        }
      }
    } catch (error) {
      console.error('Error fetching payment settings:', error);
      // Use default settings if fetch fails
      const defaultSettings: PaymentSettings = {
        upiId: 'school@ybl',
        qrCodeUrl: 'https://upload.wikimedia.org/wikipedia/commons/d/d0/QR_code_for_mobile_English_Wikipedia.svg',
        lastUpdated: new Date().toISOString()
      };
      setPaymentSettings(defaultSettings);
    }
  };

  const handlePickQrCode = async () => {
    try {
      // Request permission first
      if (Platform.OS !== 'web') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Required', 'Sorry, we need camera roll permissions to upload images!');
          return;
        }
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images', // Use string directly to avoid deprecation warnings
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.7,
        base64: true,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        // Store both URI (for display) and base64 (for upload)
        setNewQrCode(result.assets[0].uri);
        // Store base64 data if available
        if (result.assets[0].base64) {
          setQrCodeBase64(result.assets[0].base64);
        }
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const handleUpdatePaymentSettings = async () => {
    if (!newUpiId && !newQrCode) {
      Alert.alert('Error', 'Please update at least one field');
      return;
    }

    try {
      setIsLoading(true);
      const updatedSettings = { ...paymentSettings };

      // Update UPI ID if provided
      if (newUpiId) {
        updatedSettings.upiId = newUpiId;
      }

      // Store QR code directly as base64 data URI
      if (newQrCode && qrCodeBase64) {
        try {
          // Store the base64 data directly as a data URI
          // This avoids CORS issues with Storage
          updatedSettings.qrCodeUrl = `data:image/jpeg;base64,${qrCodeBase64}`;
        } catch (error) {
          console.error('Error processing image:', error);
          Alert.alert('Error', 'Failed to process QR code image. Please try again.');
          return;
        }
      }

      // Update timestamp
      updatedSettings.lastUpdated = new Date().toISOString();

      try {
        // Update Firestore
        const paymentSettingsDoc = doc(firestore, 'settings', 'payments');
        await setDoc(paymentSettingsDoc, updatedSettings);
      } catch (error) {
        console.error('Error updating payment settings:', error);
        Alert.alert('Error', 'Failed to update payment settings. Please check your connection and try again.');
        return;
      }

      // Update local state
      setPaymentSettings(updatedSettings);
      setNewUpiId('');
      setNewQrCode(null);
      setQrCodeBase64(null);
      setShowSettingsModal(false);

      Alert.alert('Success', 'Payment settings updated successfully');
    } catch (error) {
      console.error('Error updating payment settings:', error);
      Alert.alert('Error', 'Failed to update payment settings');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([fetchPayments(), fetchPaymentSettings()]);
  };

  // Use width for responsive design if needed in the future

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header - Mobile Optimized */}
      <View className="bg-white px-3 py-3 border-b border-gray-200">
        <View className="flex-row items-center mb-2">
          <TouchableOpacity
            className="mr-2 p-2 bg-gray-100 rounded-full"
            onPress={() => router.back()}
            hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
          >
            <FontAwesome name="arrow-left" size={16} color="#4B5563" />
          </TouchableOpacity>
          <Text className="text-lg font-bold text-gray-800 flex-1">
            Payment Requests
          </Text>
          <TouchableOpacity
            className="bg-blue-500 px-3 py-1.5 rounded-lg"
            onPress={() => setShowSettingsModal(true)}
          >
            <Text className="text-white text-sm font-medium">Settings</Text>
          </TouchableOpacity>
        </View>
        <Text className="text-xs text-gray-500 ml-10">
          Manage and review payment requests
        </Text>
      </View>

      {/* Filter Bar - Mobile Optimized */}
      <View className="bg-white px-3 py-2 border-b border-gray-200">
        {/* Search Bar */}
        <View className="flex-row items-center bg-gray-100 rounded-lg px-3 py-2 mb-3">
          <FontAwesome name="search" size={14} color="#6B7280" />
          <TextInput
            className="ml-2 flex-1 text-sm"
            placeholder="Search by name..."
            value={searchQuery}
            onChangeText={(text) => {
              setSearchQuery(text);
              if (text.trim() === '') {
                // If search is cleared, show filtered by status
                if (statusFilter === 'all') {
                  setFilteredPayments(payments);
                } else {
                  setFilteredPayments(payments.filter(p => p.status === statusFilter));
                }
              } else {
                // Filter by search text and status
                const filtered = payments.filter(p => {
                  const matchesSearch = p.studentName.toLowerCase().includes(text.toLowerCase()) ||
                                       p.parentName.toLowerCase().includes(text.toLowerCase());
                  const matchesStatus = statusFilter === 'all' || p.status === statusFilter;
                  return matchesSearch && matchesStatus;
                });
                setFilteredPayments(filtered);
              }
            }}
          />
        </View>

        {/* Filter Tabs */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          className="flex-row"
          contentContainerStyle={{ paddingRight: 20 }}
        >
          <TouchableOpacity
            className={`px-4 py-2 rounded-full mr-2 ${statusFilter === 'all' ? 'bg-blue-500' : 'bg-gray-100'}`}
            onPress={() => {
              setStatusFilter('all');
              setFilteredPayments(payments);
            }}
          >
            <Text className={`text-sm font-medium ${statusFilter === 'all' ? 'text-white' : 'text-gray-700'}`}>All</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className={`px-4 py-2 rounded-full mr-2 ${statusFilter === 'pending' ? 'bg-yellow-500' : 'bg-gray-100'}`}
            onPress={() => {
              setStatusFilter('pending');
              setFilteredPayments(payments.filter(p => p.status === 'pending'));
            }}
          >
            <Text className={`text-sm font-medium ${statusFilter === 'pending' ? 'text-white' : 'text-gray-700'}`}>Pending</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className={`px-4 py-2 rounded-full mr-2 ${statusFilter === 'approved' ? 'bg-green-500' : 'bg-gray-100'}`}
            onPress={() => {
              setStatusFilter('approved');
              setFilteredPayments(payments.filter(p => p.status === 'approved'));
            }}
          >
            <Text className={`text-sm font-medium ${statusFilter === 'approved' ? 'text-white' : 'text-gray-700'}`}>Approved</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className={`px-4 py-2 rounded-full mr-2 ${statusFilter === 'rejected' ? 'bg-red-500' : 'bg-gray-100'}`}
            onPress={() => {
              setStatusFilter('rejected');
              setFilteredPayments(payments.filter(p => p.status === 'rejected'));
            }}
          >
            <Text className={`text-sm font-medium ${statusFilter === 'rejected' ? 'text-white' : 'text-gray-700'}`}>Rejected</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>

      {/* Payment List */}
      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View className="p-3 space-y-3">
          {filteredPayments.length === 0 ? (
            <View className="bg-white rounded-xl py-10 px-4 shadow-sm border border-gray-100 items-center justify-center my-8">
              <FontAwesome name="search" size={32} color="#D1D5DB" />
              <Text className="text-gray-400 mt-3 text-center text-base font-medium">
                No payments found
              </Text>
              <Text className="text-gray-400 text-center text-xs mt-1 px-6">
                Try adjusting your filters or search criteria
              </Text>
            </View>
          ) : (
            filteredPayments.map((payment) => (
              <TouchableOpacity
                key={payment.id}
                className="bg-white rounded-xl p-3 shadow-sm border border-gray-100 active:bg-gray-50"
                onPress={() => {
                  setSelectedPayment(payment);
                  setShowModal(true);
                }}
                activeOpacity={0.7}
              >
                <View className="flex-row justify-between items-start">
                  <View className="flex-row items-center flex-1">
                    <View className="w-8 h-8 rounded-full bg-blue-100 items-center justify-center mr-2">
                      <Text className="font-bold text-blue-600 text-xs">
                        {payment.studentName.charAt(0)}
                      </Text>
                    </View>
                    <View className="flex-1 mr-2">
                      <Text className="text-sm font-semibold text-gray-800" numberOfLines={1}>
                        {payment.studentName}
                      </Text>
                      <View className="flex-row items-center flex-wrap">
                        <Text className="text-xs text-gray-500">
                          {payment.class}
                        </Text>
                        <Text className="text-xs text-gray-400 mx-1">•</Text>
                        <Text className="text-xs text-gray-500">
                          {format(new Date(payment.date), 'dd MMM')}
                        </Text>
                      </View>
                    </View>
                  </View>
                  <View className={`px-2 py-1 rounded-full ${getStatusColor(payment.status)}`}>
                    <Text className="text-xs font-medium capitalize">
                      {payment.status}
                    </Text>
                  </View>
                </View>

                <View className="mt-2 flex-row justify-between items-center">
                  <View className="flex-row items-center">
                    <FontAwesome name="rupee" size={14} color="#4B5563" />
                    <Text className="text-base font-bold text-gray-800 ml-1">
                      {payment.amount.toLocaleString()}
                    </Text>
                  </View>

                  {payment.status === 'pending' && (
                    <View className="flex-row">
                      <TouchableOpacity
                        className="bg-red-100 p-1.5 rounded-full mr-2"
                        onPress={() => handleRejectPayment(payment)}
                        hitSlop={{top: 5, bottom: 5, left: 5, right: 5}}
                      >
                        <FontAwesome name="times" size={14} color="#EF4444" />
                      </TouchableOpacity>
                      <TouchableOpacity
                        className="bg-green-100 p-1.5 rounded-full"
                        onPress={() => handleApprovePayment(payment)}
                        hitSlop={{top: 5, bottom: 5, left: 5, right: 5}}
                      >
                        <FontAwesome name="check" size={14} color="#10B981" />
                      </TouchableOpacity>
                    </View>
                  )}

                  {payment.status === 'approved' && (
                    <View className="flex-row items-center">
                      <FontAwesome name="check-circle" size={14} color="#10B981" />
                      <Text className="text-green-600 text-xs ml-1">Approved</Text>
                    </View>
                  )}

                  {payment.status === 'rejected' && (
                    <View className="flex-row items-center">
                      <FontAwesome name="times-circle" size={14} color="#EF4444" />
                      <Text className="text-red-600 text-xs ml-1">Rejected</Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            ))
          )}
        </View>
      </ScrollView>

      {/* Payment Detail Modal - Mobile Optimized */}
      <Modal
        visible={showModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowModal(false)}
      >
        <View className="flex-1 bg-black/50 justify-end">
          <View className="bg-white rounded-t-3xl p-3 h-[90%]">
            <View className="flex-row justify-between items-center mb-3">
              <View className="flex-row items-center">
                <View className="w-8 h-8 rounded-full bg-blue-100 items-center justify-center mr-2">
                  <Text className="font-bold text-blue-600 text-xs">
                    {selectedPayment?.studentName?.charAt(0) || 'P'}
                  </Text>
                </View>
                <Text className="text-base font-bold text-gray-800">
                  Payment Details
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => setShowModal(false)}
                className="bg-gray-100 p-1.5 rounded-full"
                hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
              >
                <FontAwesome name="close" size={16} color="#6B7280" />
              </TouchableOpacity>
            </View>

            {selectedPayment && (
              <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
                <View className="space-y-6">
                  {/* Status Banner */}
                  <View className={`p-4 rounded-xl ${selectedPayment.status === 'approved' ? 'bg-green-50' : selectedPayment.status === 'rejected' ? 'bg-red-50' : 'bg-yellow-50'}`}>
                    <View className="flex-row items-center">
                      <View className={`w-10 h-10 rounded-full items-center justify-center ${selectedPayment.status === 'approved' ? 'bg-green-100' : selectedPayment.status === 'rejected' ? 'bg-red-100' : 'bg-yellow-100'}`}>
                        <FontAwesome
                          name={selectedPayment.status === 'approved' ? 'check' : selectedPayment.status === 'rejected' ? 'times' : 'clock-o'}
                          size={20}
                          color={selectedPayment.status === 'approved' ? '#10B981' : selectedPayment.status === 'rejected' ? '#EF4444' : '#F59E0B'}
                        />
                      </View>
                      <View className="ml-3">
                        <Text className={`font-semibold ${selectedPayment.status === 'approved' ? 'text-green-700' : selectedPayment.status === 'rejected' ? 'text-red-700' : 'text-yellow-700'}`}>
                          {selectedPayment.status === 'approved' ? 'Payment Approved' : selectedPayment.status === 'rejected' ? 'Payment Rejected' : 'Payment Pending'}
                        </Text>
                        <Text className={`text-sm ${selectedPayment.status === 'approved' ? 'text-green-600' : selectedPayment.status === 'rejected' ? 'text-red-600' : 'text-yellow-600'}`}>
                          {selectedPayment.status === 'approved' ? 'This payment has been verified and approved' :
                           selectedPayment.status === 'rejected' ? 'This payment has been rejected' :
                           'This payment is awaiting your review'}
                        </Text>
                      </View>
                    </View>
                  </View>

                  {/* Amount Card */}
                  <View className="bg-blue-50 p-4 rounded-xl">
                    <Text className="text-blue-700 text-sm font-medium mb-1">Payment Amount</Text>
                    <View className="flex-row items-center">
                      <FontAwesome name="rupee" size={24} color="#1D4ED8" />
                      <Text className="text-3xl font-bold text-blue-800 ml-2">
                        {selectedPayment.amount.toLocaleString()}
                      </Text>
                    </View>
                    <View className="flex-row justify-between mt-3">
                      <View>
                        <Text className="text-xs text-blue-700">Date</Text>
                        <Text className="text-sm font-medium text-blue-900">
                          {format(new Date(selectedPayment.date), 'MMM dd, yyyy')}
                        </Text>
                      </View>
                      <View>
                        <Text className="text-xs text-blue-700">UPI Reference</Text>
                        <Text className="text-sm font-medium text-blue-900">
                          {selectedPayment.upiReferenceId}
                        </Text>
                      </View>
                    </View>
                  </View>

                  {/* Student Info */}
                  <View className="bg-white border border-gray-100 p-4 rounded-xl shadow-sm">
                    <Text className="text-sm font-medium text-gray-500 mb-3">
                      Student Information
                    </Text>
                    <View className="space-y-3">
                      <View className="flex-row items-center">
                        <View className="w-8 h-8 rounded-full bg-gray-100 items-center justify-center">
                          <FontAwesome name="user" size={16} color="#4B5563" />
                        </View>
                        <View className="ml-3">
                          <Text className="text-xs text-gray-500">Student Name</Text>
                          <Text className="text-base font-semibold text-gray-800">
                            {selectedPayment.studentName}
                          </Text>
                        </View>
                      </View>

                      <View className="flex-row items-center">
                        <View className="w-8 h-8 rounded-full bg-gray-100 items-center justify-center">
                          <FontAwesome name="graduation-cap" size={16} color="#4B5563" />
                        </View>
                        <View className="ml-3">
                          <Text className="text-xs text-gray-500">Class</Text>
                          <Text className="text-base font-semibold text-gray-800">
                            {selectedPayment.studentClass || selectedPayment.class}
                          </Text>
                        </View>
                      </View>

                      <View className="flex-row items-center">
                        <View className="w-8 h-8 rounded-full bg-gray-100 items-center justify-center">
                          <FontAwesome name="users" size={16} color="#4B5563" />
                        </View>
                        <View className="ml-3">
                          <Text className="text-xs text-gray-500">Parent</Text>
                          <Text className="text-base font-semibold text-gray-800">
                            {selectedPayment.parentName}
                          </Text>
                        </View>
                      </View>

                      {selectedPayment.mobileNumber && (
                        <View className="flex-row items-center">
                          <View className="w-8 h-8 rounded-full bg-gray-100 items-center justify-center">
                            <FontAwesome name="phone" size={16} color="#4B5563" />
                          </View>
                          <View className="ml-3 flex-1">
                            <Text className="text-xs text-gray-500">Mobile</Text>
                            <View className="flex-row items-center justify-between">
                              <Text className="text-base font-semibold text-gray-800">
                                {selectedPayment.mobileNumber}
                              </Text>
                              <TouchableOpacity
                                className="bg-blue-100 p-2 rounded-full"
                                onPress={() => Linking.openURL(`tel:${selectedPayment.mobileNumber}`)}
                              >
                                <FontAwesome name="phone" size={16} color="#3B82F6" />
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      )}
                    </View>
                  </View>

                  {/* Screenshot */}
                  <View className="bg-white border border-gray-100 p-4 rounded-xl shadow-sm">
                    <Text className="text-sm font-medium text-gray-500 mb-3">
                      Payment Screenshot
                    </Text>
                    <View className="relative overflow-hidden rounded-xl">
                      <TouchableOpacity
                        onPress={() => {
                          setSelectedImage(selectedPayment.screenshot);
                          setShowImageModal(true);
                        }}
                      >
                        {selectedPayment.screenshot.includes('.json') ? (
                          // For Realtime Database images, we need to fetch the base64 data
                          <RealtimeDatabaseImage
                            url={selectedPayment.screenshot}
                            onLoadStart={() => setImageLoading(true)}
                            onLoadEnd={() => setImageLoading(false)}
                          />
                        ) : (
                          // For regular images
                          <Image
                            source={{ uri: selectedPayment.screenshot }}
                            className="w-full h-56 rounded-xl bg-gray-100"
                            resizeMode="contain"
                            onLoadStart={() => setImageLoading(true)}
                            onLoadEnd={() => setImageLoading(false)}
                          />
                        )}
                        {imageLoading && (
                          <View className="absolute inset-0 items-center justify-center">
                            <ActivityIndicator size="large" color="#3B82F6" />
                          </View>
                        )}
                        <View className="absolute bottom-2 right-2 bg-black/70 px-3 py-2 rounded-lg flex-row items-center">
                          <FontAwesome name="search-plus" size={14} color="white" />
                          <Text className="text-white text-xs ml-1">Tap to enlarge</Text>
                        </View>
                      </TouchableOpacity>
                    </View>
                  </View>

                  {/* Notes */}
                  {selectedPayment.notes && (
                    <View className="bg-white border border-gray-100 p-4 rounded-xl shadow-sm">
                      <Text className="text-sm font-medium text-gray-500 mb-2">
                        Notes
                      </Text>
                      <Text className="text-gray-700">
                        {selectedPayment.notes}
                      </Text>
                    </View>
                  )}

                  {/* Action Buttons */}
                  {selectedPayment.status === 'pending' && (
                    <View className="flex-row space-x-4 mt-4 mb-6">
                      <TouchableOpacity
                        className="flex-1 bg-white border border-red-500 p-4 rounded-xl flex-row justify-center items-center space-x-2"
                        onPress={() => handleRejectPayment(selectedPayment)}
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <ActivityIndicator color="#EF4444" />
                        ) : (
                          <>
                            <FontAwesome name="times" size={16} color="#EF4444" />
                            <Text className="text-red-500 font-semibold">
                              Reject
                            </Text>
                          </>
                        )}
                      </TouchableOpacity>
                      <TouchableOpacity
                        className="flex-1 bg-green-500 p-4 rounded-xl flex-row justify-center items-center space-x-2"
                        onPress={() => handleApprovePayment(selectedPayment)}
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <ActivityIndicator color="white" />
                        ) : (
                          <>
                            <FontAwesome name="check" size={16} color="white" />
                            <Text className="text-white font-semibold">
                              Approve
                            </Text>
                          </>
                        )}
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              </ScrollView>
            )}
          </View>
        </View>
      </Modal>

      {/* Full Screen Image Modal */}
      <Modal
        visible={showImageModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowImageModal(false)}
      >
        <View className="flex-1 bg-black/90 justify-center items-center">
          <TouchableOpacity
            className="absolute top-10 right-5 z-10 p-2 bg-black/50 rounded-full"
            onPress={() => setShowImageModal(false)}
          >
            <FontAwesome name="close" size={24} color="white" />
          </TouchableOpacity>

          <View className="w-full h-full justify-center items-center">
            {selectedImage ? (
              selectedImage.includes('.json') ? (
                // For Realtime Database images
                <RealtimeDatabaseImage
                  url={selectedImage}
                  onLoadStart={() => {}}
                  onLoadEnd={() => {}}
                />
              ) : (
                // For regular images
                <Image
                  source={{ uri: selectedImage }}
                  className="w-full h-3/4"
                  resizeMode="contain"
                />
              )
            ) : (
              <ActivityIndicator size="large" color="white" />
            )}
          </View>
        </View>
      </Modal>

      {/* Note Input Modal */}
      <Modal
        visible={showNoteModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowNoteModal(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center">
          <View className="bg-white m-4 p-4 rounded-lg">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-xl font-bold">
                {noteAction === 'approve' ? 'Approve Payment' : 'Reject Payment'}
              </Text>
              <TouchableOpacity onPress={() => setShowNoteModal(false)}>
                <FontAwesome name="close" size={24} color="#6b7280" />
              </TouchableOpacity>
            </View>

            <Text className="text-gray-700 mb-2">
              {noteAction === 'approve'
                ? 'Add any notes about this payment approval (optional)'
                : 'Add a reason for rejecting this payment (optional)'}
            </Text>

            <TextInput
              className="border border-gray-300 rounded-md p-2 mb-4"
              placeholder="Enter note here"
              value={adminNote}
              onChangeText={setAdminNote}
              multiline
              numberOfLines={3}
            />

            <View className="flex-row space-x-2">
              <TouchableOpacity
                className="flex-1 bg-gray-200 p-3 rounded-md"
                onPress={() => setShowNoteModal(false)}
              >
                <Text className="text-center font-semibold">Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className={`flex-1 p-3 rounded-md ${noteAction === 'approve' ? 'bg-green-500' : 'bg-red-500'}`}
                onPress={() => {
                  if (!selectedPayment) {
                    Alert.alert('Error', 'No payment selected');
                    return;
                  }

                  if (noteAction === 'approve') {
                    confirmApprovePayment(selectedPayment);
                  } else {
                    confirmRejectPayment(selectedPayment);
                  }
                }}
                disabled={isLoading}
              >
                {isLoading ? (
                  <ActivityIndicator color="white" />
                ) : (
                  <Text className="text-white text-center font-semibold">
                    {noteAction === 'approve' ? 'Approve' : 'Reject'}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Payment Settings Modal */}
      <Modal
        visible={showSettingsModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowSettingsModal(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center">
          <View className="bg-white m-4 p-4 rounded-lg">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-xl font-bold">Update Payment Settings</Text>
              <TouchableOpacity onPress={() => setShowSettingsModal(false)}>
                <FontAwesome name="close" size={24} color="#6b7280" />
              </TouchableOpacity>
            </View>

            <View className="mb-4">
              <Text className="text-gray-700 mb-1">UPI ID</Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-2"
                value={newUpiId}
                onChangeText={setNewUpiId}
                placeholder={paymentSettings.upiId}
                placeholderTextColor="#9ca3af"
              />
            </View>

            <View className="mb-4">
              <Text className="text-gray-700 mb-1">QR Code</Text>
              {newQrCode ? (
                <View className="items-center mb-2">
                  <Image source={{ uri: newQrCode }} className="w-40 h-40 rounded-md" />
                </View>
              ) : (
                <View className="items-center mb-2">
                  <Image source={{ uri: paymentSettings.qrCodeUrl }} className="w-40 h-40 rounded-md" />
                  <Text className="text-gray-500 text-xs mt-1">Current QR Code</Text>
                </View>
              )}
              <TouchableOpacity
                className="bg-gray-200 p-2 rounded-lg items-center"
                onPress={handlePickQrCode}
              >
                <Text className="text-gray-700">
                  {newQrCode ? 'Change QR Code' : 'Upload New QR Code'}
                </Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              className="bg-blue-500 p-3 rounded-lg items-center mt-4"
              onPress={handleUpdatePaymentSettings}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#ffffff" size="small" />
              ) : (
                <Text className="text-white font-medium">Update Settings</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Payment Settings Modal */}
      <Modal
        visible={showSettingsModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowSettingsModal(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center">
          <View className="bg-white m-4 p-4 rounded-lg">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-xl font-bold">Update Payment Settings</Text>
              <TouchableOpacity onPress={() => setShowSettingsModal(false)}>
                <FontAwesome name="close" size={24} color="#6b7280" />
              </TouchableOpacity>
            </View>

            <View className="mb-4">
              <Text className="text-gray-700 mb-1">UPI ID</Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-2"
                value={newUpiId}
                onChangeText={setNewUpiId}
                placeholder={paymentSettings.upiId}
                placeholderTextColor="#9ca3af"
              />
            </View>

            <View className="mb-4">
              <Text className="text-gray-700 mb-1">QR Code</Text>
              {newQrCode ? (
                <View className="items-center mb-2">
                  <Image source={{ uri: newQrCode }} className="w-40 h-40 rounded-md" />
                </View>
              ) : (
                <View className="items-center mb-2">
                  <Image source={{ uri: paymentSettings.qrCodeUrl }} className="w-40 h-40 rounded-md" />
                  <Text className="text-gray-500 text-xs mt-1">Current QR Code</Text>
                </View>
              )}
              <TouchableOpacity
                className="bg-gray-200 p-2 rounded-lg items-center"
                onPress={handlePickQrCode}
              >
                <Text className="text-gray-700">
                  {newQrCode ? 'Change QR Code' : 'Upload New QR Code'}
                </Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              className="bg-blue-500 p-3 rounded-lg items-center mt-4"
              onPress={handleUpdatePaymentSettings}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#ffffff" size="small" />
              ) : (
                <Text className="text-white font-medium">Update Settings</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}