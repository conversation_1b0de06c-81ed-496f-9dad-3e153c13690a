import './global.css';
import { View, Image, Animated, Dimensions, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'expo-status-bar';
import { useAuth } from '../hooks/useAuth';
// Navigation is handled by _layout.tsx
import { useEffect, useRef } from 'react';

// Get screen dimensions
const { width, height } = Dimensions.get('window');

// Define styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    position: 'absolute',
    width: width,
    height: height,
    opacity: 0.06,
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  topSection: {
    width: '100%',
    height: height * 0.45,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 20,
  },
  bottomSection: {
    width: '100%',
    flex: 1,
    backgroundColor: 'rgba(103, 58, 183, 0.9)',  // Vibrant purple base
    borderTopLeftRadius: 50,
    borderTopRightRadius: 50,
    padding: 30,
    alignItems: 'center',
    justifyContent: 'flex-start',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 25,
    borderWidth: 8,
    borderColor: '#FFEB3B',  // Yellow border
    borderStyle: 'dotted',
  },
  logoContainer: {
    width: width * 0.85,
    height: width * 0.28,
    backgroundColor: 'white',
    borderRadius: 25,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 15 },
    shadowOpacity: 0.4,
    shadowRadius: 20,
    elevation: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 6,
    borderColor: '#E91E63',  // Bright pink border
    borderStyle: 'solid',
    transform: [{ rotate: '-2deg' }],  // Slightly tilted for playful look
  },
  logo: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  schoolName: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#FFEB3B',  // Bright yellow text
    textAlign: 'center',
    marginTop: 30,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 1,
  },
  tagline: {
    fontSize: 20,
    color: '#4CAF50',  // Green text
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 10,
    fontStyle: 'italic',
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
    letterSpacing: 0.5,
  },
  // divider: {
  //   width: 150,
  //   height: 8,
  //   backgroundColor: '#FF4081',  // Pink divider
  //   borderRadius: 4,
  //   marginVertical: 25,
  //   transform: [{ rotate: '-2deg' }],  // Slightly tilted for playful look
  // },
  welcomeText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#00BCD4',  // Cyan text
    marginTop: 10,
    textShadowColor: 'rgba(0, 0, 0, 0.4)',
    textShadowOffset: { width: 1, height: 2 },
    textShadowRadius: 3,
    letterSpacing: 1,
    transform: [{ rotate: '1deg' }],  // Slightly tilted for playful look
  },
  infoText: {
    fontSize: 20,
    color: '#FFEB3B',  // Bright yellow text
    textAlign: 'center',
    marginTop: 15,
    maxWidth: 400,
    lineHeight: 28,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  versionText: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    fontSize: 12,
    color: '#FF9800',  // Orange text
    fontWeight: '500',
    transform: [{ rotate: '-2deg' }],  // Slightly tilted for playful look
  }
});

export default function Index() {
  const { user } = useAuth();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(100)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    // Start animations immediately
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      })
    ]).start();

    // No redirection logic here - let _layout.tsx handle it
    // This prevents conflicts between multiple navigation attempts
  }, []);

  // If user is logged in, show only the logo with version at bottom
  if (user) {
    return (
      <View style={styles.container}>
        <StatusBar style="light" />

        <LinearGradient
          colors={['#FF1493', '#FF4500', '#FFD700', '#32CD32', '#1E90FF', '#8A2BE2']}  // Rainbow colors for children
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.container}>

          {/* Background pattern */}
          <Image
            source={require('../assets/logo.jpg')}
            style={styles.backgroundImage}
            resizeMode="cover"
          />

          {/* Top-bottom layout */}
          <View style={styles.contentContainer}>
            {/* Top section with logo */}
            <Animated.View
              style={[styles.topSection, { opacity: fadeAnim }]}
            >
              <Animated.View style={{
                transform: [{ scale: scaleAnim }, { rotate: '-3deg' }],
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 20 },
                shadowOpacity: 0.4,
                shadowRadius: 30,
              }}>
                <View style={[styles.logoContainer, {
                  borderWidth: 8,
                  borderColor: '#FF9800',  // Orange border
                  borderStyle: 'solid',
                  backgroundColor: 'rgba(255, 255, 255, 0.95)',
                }]}>
                  <Image
                    source={require('../assets/logo.jpg')}
                    style={styles.logo}
                  />
                </View>
              </Animated.View>
            </Animated.View>

            {/* Bottom section with content */}
            <Animated.View
              style={[styles.bottomSection, {
                transform: [{ translateY: slideAnim }]
              }]}
            >
              <Animated.Text
                style={[styles.schoolName, { opacity: fadeAnim }]}
              >
                School Name
              </Animated.Text>

              <Animated.Text
                style={[styles.tagline, { opacity: fadeAnim }]}
              >
                Education for a brighter future
              </Animated.Text>

              <View style={styles.divider} />

              <Animated.Text style={styles.welcomeText}>
                Hello, Little Champion! 🌈🎉👋
              </Animated.Text>

              <Animated.Text style={styles.infoText}>
                Let's explore, create, and learn amazing things today! 🎨✨🚀🌟
              </Animated.Text>
            </Animated.View>
          </View>

          {/* Version at bottom with subtle animation */}
          <Animated.Text style={[
            styles.versionText,
            { opacity: fadeAnim }
          ]}>
            Version 1.0.0
          </Animated.Text>
        </LinearGradient>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" />

      <LinearGradient
        colors={['#FF1493', '#FF4500', '#FFD700', '#32CD32', '#1E90FF', '#8A2BE2']}  // Rainbow colors for children
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.container}>

        {/* Background pattern */}
        <Image
          source={require('../assets/logo.jpg')}
          style={styles.backgroundImage}
          resizeMode="cover"
        />

        {/* Top-bottom layout */}
        <View style={styles.contentContainer}>
          {/* Top section with logo */}
          <Animated.View
            style={[styles.topSection, { opacity: fadeAnim }]}
          >
            <Animated.View style={{
              transform: [{ scale: scaleAnim }],
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 10 },
              shadowOpacity: 0.3,
              shadowRadius: 15,
            }}>
              <View style={styles.logoContainer}>
                <Image
                  source={require('../assets/logo.jpg')}
                  style={styles.logo}
                />
              </View>
            </Animated.View>
          </Animated.View>

          {/* Bottom section with content */}
          <Animated.View
            style={[styles.bottomSection, {
              transform: [{ translateY: slideAnim }]
            }]}
          >
            <Animated.Text
              style={[styles.schoolName, { opacity: fadeAnim }]}
            >
              School Name
            </Animated.Text>

            <Animated.Text
              style={[styles.tagline, { opacity: fadeAnim }]}
            >
              Education for a brighter future
            </Animated.Text>

            <View style={styles.divider} />

            <Animated.Text style={styles.welcomeText}>
              Welcome to Fun School! 🎉🌟
            </Animated.Text>

            <Animated.Text style={styles.infoText}>
              Let's start our colorful learning journey! 🌈📚🌟
            </Animated.Text>
          </Animated.View>
        </View>

        {/* Version at bottom with subtle animation */}
        <Animated.Text style={[
          styles.versionText,
          { opacity: fadeAnim }
        ]}>
          Version 1.0.0
        </Animated.Text>
      </LinearGradient>
    </View>
  );
}
