import { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert, Image, Modal, ActivityIndicator, Platform, Linking, Share } from 'react-native';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../../hooks/useAuth';
import { FontAwesome } from '@expo/vector-icons';
// Import ImagePicker with a fallback for compatibility
let ImagePicker: any;
try {
  ImagePicker = require('expo-image-picker');
} catch (error) {
  console.warn('expo-image-picker not available, using fallback');
  // Fallback implementation
  ImagePicker = {
    // Simple fallback that doesn't use deprecated properties
    requestMediaLibraryPermissionsAsync: async () => ({ status: 'granted' }),
    launchImageLibraryAsync: async (options: any) => {
      Alert.alert('Not Available', 'Image picker is not available on this device.');
      return { cancelled: true };
    }
  };
}
import { firestore, storage } from '../../config/firebase';
import { collection, addDoc, getDocs, query, where, orderBy, doc, getDoc, updateDoc } from 'firebase/firestore';
import { ref as storageRef, uploadBytes, getDownloadURL } from 'firebase/storage';

interface Child {
  id: string;
  name: string;
  class: string;
}

interface Payment {
  id: string;
  amount: number;
  upiReferenceId: string;
  screenshot: string;
  date: string;
  status: 'pending' | 'approved' | 'rejected';
  studentId: string;
  studentName?: string;
  studentClass?: string;
  parentId?: string;
  parentName?: string;
  notes?: string;
}

export default function ParentPayment() {
  const { user } = useAuth();
  const [showChildModal, setShowChildModal] = useState(false);
  const [selectedChild, setSelectedChild] = useState<Child | null>(null);
  const [children, setChildren] = useState<Child[]>([]);
  const [loading, setLoading] = useState(false);

  // Payment settings state
  const [paymentSettings, setPaymentSettings] = useState({
    upiId: 'school@ybl',
    qrCodeUrl: 'https://upload.wikimedia.org/wikipedia/commons/d/d0/QR_code_for_mobile_English_Wikipedia.svg',
    lastUpdated: new Date().toISOString()
  });

  // Fetch data from Firestore when user is available
  useEffect(() => {
    if (user) {
      fetchChildren();
      fetchPaymentSettings();
      // Don't fetch payments here, we'll do it when selectedChild changes
    }
  }, [user]);

  // Fetch payments when selected child changes
  useEffect(() => {
    if (user) {
      fetchPayments();
    }
  }, [user, selectedChild]);

  const [amount, setAmount] = useState('');
  const [upiReferenceId, setUpiReferenceId] = useState('');
  const [screenshot, setScreenshot] = useState<string | null>(null);
  const [screenshotBase64, setScreenshotBase64] = useState<string | null>(null);
  const [payments, setPayments] = useState<Payment[]>([]);

  const fetchChildren = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const childrenCollection = collection(firestore, 'students');

      // Get students where parentPhone matches user's phone number
      // If user doesn't have a phone number, fall back to parentId
      let childrenQuery;
      if (user.phone) {
        console.log('Fetching children by phone number:', user.phone);
        childrenQuery = query(childrenCollection, where('parentPhone', '==', user.phone));
      } else {
        console.log('No phone number found, fetching by parentId');
        childrenQuery = query(childrenCollection, where('parentId', '==', user.id));
      }

      const querySnapshot = await getDocs(childrenQuery);

      const childrenData: Child[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        childrenData.push({
          id: doc.id,
          name: data.name || 'Unknown',
          class: data.class || 'Unknown',
        });
      });

      console.log(`Found ${childrenData.length} children for this parent`);
      setChildren(childrenData);

      // Try to get previously selected child from AsyncStorage
      try {
        const savedChildId = await AsyncStorage.getItem('selectedChildId');
        console.log('Retrieved saved child ID:', savedChildId);

        if (savedChildId) {
          // Find the child with the saved ID
          const savedChild = childrenData.find(child => child.id === savedChildId);
          if (savedChild) {
            console.log('Found saved child:', savedChild.name);
            setSelectedChild(savedChild);
          } else {
            // If saved child not found (maybe removed), select first child
            console.log('Saved child not found in current children list');
            if (childrenData.length > 0) {
              setSelectedChild(childrenData[0]);
            }
          }
        } else if (childrenData.length > 0 && !selectedChild) {
          // If no saved child, select the first one
          setSelectedChild(childrenData[0]);
        }
      } catch (error) {
        console.error('Error retrieving selected child from AsyncStorage:', error);
        // Fallback to selecting first child
        if (childrenData.length > 0 && !selectedChild) {
          setSelectedChild(childrenData[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching children:', error);
      Alert.alert('Error', 'Failed to load children data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchPaymentSettings = async () => {
    try {
      // Use Firestore for payment settings
      const paymentSettingsDoc = doc(firestore, 'settings', 'payments');
      const snapshot = await getDoc(paymentSettingsDoc);

      if (snapshot.exists()) {
        const data = snapshot.data();
        setPaymentSettings(data);
      }
    } catch (error) {
      console.error('Error fetching payment settings:', error);
      // Continue with default settings if there's an error
    }
  };

  const fetchPayments = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const paymentsCollection = collection(firestore, 'payments');

      // Create query based on whether a child is selected
      let paymentsQuery;

      if (selectedChild) {
        // If a child is selected, filter by both parentId and studentId
        console.log(`Fetching payments for specific child: ${selectedChild.name} (${selectedChild.id})`);
        paymentsQuery = query(
          paymentsCollection,
          where('parentId', '==', user.id),
          where('studentId', '==', selectedChild.id)
        );
      } else {
        // If no child is selected, just filter by parentId
        console.log('Fetching all payments for parent');
        paymentsQuery = query(
          paymentsCollection,
          where('parentId', '==', user.id)
        );
      }
      const querySnapshot = await getDocs(paymentsQuery);

      const paymentsData: Payment[] = [];

      for (const doc of querySnapshot.docs) {
        const data = doc.data();

        // Get screenshot URL
        let screenshotUrl = '';
        try {
          if (data.screenshotPath) {
            const imageRef = storageRef(storage, data.screenshotPath);
            screenshotUrl = await getDownloadURL(imageRef);
          }
        } catch (error) {
          console.error('Error getting screenshot URL:', error);
          screenshotUrl = 'https://via.placeholder.com/400x300?text=No+Screenshot';
        }

        paymentsData.push({
          id: doc.id,
          amount: data.amount || 0,
          upiReferenceId: data.upiReferenceId || 'N/A',
          screenshot: screenshotUrl,
          date: data.date || new Date().toISOString().split('T')[0],
          status: data.status || 'pending',
          studentId: data.studentId || '',
          studentName: data.studentName || 'Unknown Student',
          studentClass: data.studentClass || 'Unknown Class',
          parentId: data.parentId || '',
          parentName: data.parentName || 'Parent',
          notes: data.notes || '',
        });
      }

      // Sort payments by date in descending order (newest first)
      paymentsData.sort((a, b) => {
        const dateA = new Date(a.date).getTime();
        const dateB = new Date(b.date).getTime();
        return dateB - dateA; // Descending order
      });

      setPayments(paymentsData);
    } catch (error) {
      console.error('Error fetching payments:', error);
      Alert.alert('Error', 'Failed to load payments. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectChild = async (child: Child) => {
    console.log(`Selecting child: ${child.name} (${child.id})`);
    setSelectedChild(child);
    setShowChildModal(false);

    // Show loading indicator while we fetch payments
    setLoading(true);

    // Save selected child to AsyncStorage
    try {
      await AsyncStorage.setItem('selectedChildId', child.id);
      console.log(`Saved selected child ID ${child.id} to AsyncStorage`);

      // Note: We don't need to call fetchPayments() here because the useEffect will trigger
      // when selectedChild changes
    } catch (error) {
      console.error('Error saving selected child to AsyncStorage:', error);
    }
  };

  const handleSelectImage = async () => {
    try {
      console.log('Starting image selection process...');

      // Check if ImagePicker is available
      if (!ImagePicker) {
        console.error('ImagePicker is not available');
        Alert.alert('Error', 'Image picker is not available on this device.');
        return;
      }

      // Request permission first on native platforms
      if (Platform.OS !== 'web') {
        try {
          console.log('Requesting media library permissions...');
          const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
          console.log('Permission result:', permissionResult);

          if (permissionResult.status !== 'granted') {
            Alert.alert('Permission Required', 'Sorry, we need camera roll permissions to upload images!');
            return;
          }
        } catch (permError) {
          console.error('Error requesting permissions:', permError);
          // Continue anyway - some expo versions don't require explicit permissions
        }
      }

      // Launch image picker with safe options
      console.log('Launching image picker...');
      const pickerOptions = {
        mediaTypes: 'images', // Use string directly to avoid deprecation warnings
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.7,
        base64: true,
      };

      // Show loading indicator while image picker is open
      setLoading(true);

      const result = await ImagePicker.launchImageLibraryAsync(pickerOptions);
      console.log('Image picker result received:', result ? 'success' : 'null');

      // Hide loading indicator
      setLoading(false);

      // Check if the user cancelled the operation
      if ((result.canceled || result.cancelled) && !result.assets) {
        console.log('Image selection was cancelled by user');
        return;
      }

      // Handle the result based on Expo SDK version
      // Newer versions (SDK 46+) use assets array
      if (result.assets && result.assets.length > 0) {
        console.log('Using assets array format (newer Expo SDK)');
        const asset = result.assets[0];

        // Check if we have a valid URI
        if (!asset.uri) {
          console.error('No URI in the selected image asset');
          Alert.alert('Error', 'Failed to get image. Please try again.');
          return;
        }

        setScreenshot(asset.uri);

        // If base64 is not available in the result, try to get it manually
        if (!asset.base64) {
          console.log('Base64 not included in result, attempting to get it manually...');
          try {
            // This is a simplified approach - in a real app you might need a more robust solution
            // such as using expo-file-system to read the file and convert it
            const response = await fetch(asset.uri);
            const blob = await response.blob();
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onloadend = () => {
              const base64data = reader.result;
              if (typeof base64data === 'string') {
                // Extract just the base64 part without the data URL prefix
                const base64Content = base64data.split(',')[1];
                setScreenshotBase64(base64Content);
                console.log('Successfully obtained base64 data manually');
              }
            };
          } catch (base64Error) {
            console.error('Error getting base64 data manually:', base64Error);
            // Continue without base64 data, we'll handle this in the submit function
          }
        } else {
          setScreenshotBase64(asset.base64);
          console.log('Base64 data included in result');
        }
      }
      // Older versions use canceled/cancelled and direct uri
      else if ((!result.cancelled && result.uri) || (!result.canceled && result.uri)) {
        console.log('Using direct uri format (older Expo SDK)');
        setScreenshot(result.uri);

        if (result.base64) {
          setScreenshotBase64(result.base64);
        } else {
          // Try to get base64 manually similar to above
          console.log('Base64 not included in result, attempting to get it manually...');
          try {
            const response = await fetch(result.uri);
            const blob = await response.blob();
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onloadend = () => {
              const base64data = reader.result;
              if (typeof base64data === 'string') {
                const base64Content = base64data.split(',')[1];
                setScreenshotBase64(base64Content);
              }
            };
          } catch (base64Error) {
            console.error('Error getting base64 data manually:', base64Error);
          }
        }
      } else {
        console.log('Image selection format not recognized');
        Alert.alert('Error', 'Could not process the selected image. Please try again.');
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      setLoading(false);

      // Fallback to a simpler approach if the standard method fails
      Alert.alert(
        'Image Selection Failed',
        'The image picker encountered an error. Please try again or use a different device.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleSubmitPayment = async () => {
    if (!selectedChild) {
      Alert.alert('Error', 'Please select a child');
      return;
    }
    if (!amount || !upiReferenceId || !screenshot) {
      Alert.alert('Error', 'Please fill all fields and upload screenshot');
      return;
    }

    try {
      setLoading(true);
      console.log('Starting payment submission process...');

      // Validate amount is a number
      const numericAmount = parseFloat(amount);
      if (isNaN(numericAmount) || numericAmount <= 0) {
        Alert.alert('Error', 'Please enter a valid amount');
        setLoading(false);
        return;
      }

      // Generate a unique ID for the screenshot in Realtime Database
      const timestamp = Date.now();
      const paymentId = `${user?.id}_${timestamp}`;
      const screenshotPath = `payments/${paymentId}`;
      console.log('Screenshot path in Realtime Database:', screenshotPath);

      // Check if we have the base64 data
      if (!screenshotBase64) {
        console.error('No base64 data available for the screenshot');
        Alert.alert('Error', 'Failed to process screenshot. Please try again.');
        setLoading(false);
        return;
      }

      // Clean the base64 string if needed
      let base64Clean = screenshotBase64;
      if (base64Clean.includes('base64,')) {
        base64Clean = base64Clean.split('base64,')[1];
      }

      // Create a reference to the file location in Firebase Storage
      const imageRef = storageRef(storage, screenshotPath);

      // Convert base64 to blob
      const byteCharacters = atob(base64Clean);
      const byteArrays = [];
      for (let offset = 0; offset < byteCharacters.length; offset += 512) {
        const slice = byteCharacters.slice(offset, offset + 512);
        const byteNumbers = new Array(slice.length);
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
      }
      const blob = new Blob(byteArrays, { type: 'image/jpeg' });

      // Try to upload the image to Firebase Storage
      let uploadSuccess = false;
      let downloadURL = null;

      try {
        console.log('Uploading image to Firebase Storage...');

        // Create metadata including CORS headers
        const metadata = {
          contentType: 'image/jpeg',
          customMetadata: {
            'uploaded-by': user?.id || 'unknown',
            'payment-for': selectedChild.name,
            'upload-timestamp': timestamp.toString(),
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'storage-bucket': 'test-7e932.firebasestorage.app'
          }
        };

        // Upload to Firebase Storage
        await uploadBytes(imageRef, blob, metadata);
        uploadSuccess = true;
        console.log('Image uploaded to Firebase Storage successfully');

        // Get download URL
        try {
          downloadURL = await getDownloadURL(imageRef);
          console.log('Download URL obtained:', downloadURL);
        } catch (urlError) {
          console.error('Error getting download URL:', urlError);
          // Continue even if we can't get the download URL
        }
      } catch (storageError) {
        console.error('Error uploading to Firebase Storage:', storageError);
        console.log('Continuing without image');
      }

      // Add payment to Firestore with additional fields
      console.log('Adding payment data to Firestore...');
      const paymentData = {
        studentId: selectedChild.id,
        studentName: selectedChild.name,
        studentClass: selectedChild.class,
        parentId: user?.id,
        parentName: user?.name || 'Parent',
        amount: numericAmount,
        upiReferenceId,
        // Include screenshot info only if upload was successful
        ...(uploadSuccess ? {
          screenshotPath,
          // Use the download URL if available, otherwise just store the path
          screenshotUrl: downloadURL || '',
          isFirebaseStorage: true,  // Flag to indicate this is stored in Firebase Storage
          storageBucket: 'test-7e932.firebasestorage.app'
        } : {
          // If upload failed, note that in the record
          screenshotPath: 'upload_failed',
          screenshotUrl: '',
          uploadFailed: true
        }),
        date: new Date().toISOString().split('T')[0],
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        notes: ''
      };

      const docRef = await addDoc(collection(firestore, 'payments'), paymentData);
      console.log('Payment document created with ID:', docRef.id);

      // Create new payment object for local state
      const newPayment: Payment = {
        id: docRef.id,
        amount: numericAmount,
        upiReferenceId,
        screenshot: uploadSuccess ? downloadURL : '',
        date: new Date().toISOString().split('T')[0],
        status: 'pending',
        studentId: selectedChild.id,
        studentName: selectedChild.name,
        studentClass: selectedChild.class,
        parentId: user?.id || '',
        parentName: user?.name || 'Parent',
        notes: '',
      };

      // Update local state
      setPayments([newPayment, ...payments]);
      setAmount('');
      setUpiReferenceId('');
      setScreenshot(null);
      setScreenshotBase64(null);
      console.log('Payment submission completed successfully');
      Alert.alert('Success', 'Payment submitted successfully');
    } catch (error) {
      console.error('Error submitting payment:', error);
      Alert.alert('Error', 'Failed to submit payment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Function to open UPI payment apps
  const handlePayNow = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    // Check if the platform is mobile
    if (Platform.OS === 'web') {
      Alert.alert('Info', 'UPI payments are only available on mobile devices');
      return;
    }

    setLoading(true);
    console.log('Starting UPI payment process...');

    try {
      // Create a transaction ID
      const transactionId = `SCH${Date.now()}`;

      // Create a simple UPI URL that works with most UPI apps in India
      // The key is to keep it simple with just the essential parameters
      const upiUrl = `upi://pay?pa=${paymentSettings.upiId}&pn=School&am=${amount}&cu=INR`;

      console.log('Opening UPI URL:', upiUrl);

      // Check if the device can handle this UPI URL
      const canOpenUrl = await Linking.canOpenURL(upiUrl);

      if (canOpenUrl) {
        // This will open the UPI URL and show a chooser with all available UPI apps
        await Linking.openURL(upiUrl);
        console.log('UPI URL opened successfully');
      } else {
        console.log('Device cannot open UPI URL, trying alternative approaches...');

        // Try specific UPI app schemes one by one
        const upiApps = [
          { name: 'Google Pay', scheme: `tez://upi/pay?pa=${paymentSettings.upiId}&pn=School&am=${amount}&cu=INR` },
          { name: 'PhonePe', scheme: `phonepe://pay?pa=${paymentSettings.upiId}&pn=School&am=${amount}&cu=INR` },
          { name: 'Paytm', scheme: `paytmmp://pay?pa=${paymentSettings.upiId}&pn=School&am=${amount}&cu=INR` },
          { name: 'BHIM', scheme: `bhim://pay?pa=${paymentSettings.upiId}&pn=School&am=${amount}&cu=INR` },
        ];

        let appFound = false;

        for (const app of upiApps) {
          try {
            const canOpenApp = await Linking.canOpenURL(app.scheme);
            if (canOpenApp) {
              console.log(`Found UPI app: ${app.name}, opening...`);
              await Linking.openURL(app.scheme);
              appFound = true;
              break;
            }
          } catch (error) {
            console.log(`Error checking ${app.name}:`, error);
          }
        }

        if (!appFound) {
          // If all else fails, try a web URL for UPI payment
          // This will open the browser which might redirect to a UPI app
          const webUpiUrl = `https://pay.google.com/gp/v/send?pa=${paymentSettings.upiId}&pn=School&am=${amount}&cu=INR`;

          try {
            await Linking.openURL(webUpiUrl);
            console.log('Opened web UPI URL');
          } catch (webError) {
            console.error('Error opening web UPI URL:', webError);
            Alert.alert(
              'UPI Payment',
              'Could not open UPI payment apps. Please scan the QR code with your UPI app to make the payment.',
              [{ text: 'OK' }]
            );
          }
        }
      }
    } catch (error) {
      console.error('Error in UPI payment process:', error);
      Alert.alert(
        'Payment Error',
        'There was an error opening UPI payment. Please try manually using the QR code.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return '#10B981';
      case 'rejected':
        return '#EF4444';
      case 'pending':
        return '#F59E0B';
      default:
        return '#6B7280';
    }
  };

  // Function to generate and download receipt
  const generateReceipt = async (payment: Payment) => {
    try {
      // Find the child associated with this payment
      const childInfo = children.find(child => child.id === payment.studentId) || {
        name: 'Unknown Student',
        class: 'Unknown Class'
      };

      // Format the date
      const paymentDate = new Date(payment.date);
      const formattedDate = paymentDate.toLocaleDateString('en-IN', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });

      // Generate HTML for the receipt
      const html = `
        <html>
          <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
            <style>
              body {
                font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
                padding: 20px;
                color: #333;
              }
              .receipt {
                max-width: 500px;
                margin: 0 auto;
                border: 1px solid #ddd;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
              }
              .header {
                text-align: center;
                padding-bottom: 15px;
                border-bottom: 2px solid #10B981;
                margin-bottom: 20px;
              }
              .school-name {
                font-size: 24px;
                font-weight: bold;
                color: #10B981;
                margin-bottom: 5px;
              }
              .receipt-title {
                font-size: 18px;
                color: #666;
                margin-bottom: 15px;
              }
              .info-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 1px solid #eee;
              }
              .label {
                font-weight: bold;
                color: #555;
              }
              .value {
                text-align: right;
              }
              .amount {
                font-size: 22px;
                font-weight: bold;
                color: #10B981;
                text-align: center;
                padding: 15px 0;
                margin: 15px 0;
                background-color: #f8f8f8;
                border-radius: 5px;
              }
              .footer {
                text-align: center;
                margin-top: 30px;
                font-size: 12px;
                color: #999;
              }
              .status {
                display: inline-block;
                padding: 5px 10px;
                border-radius: 15px;
                font-size: 14px;
                font-weight: bold;
                color: white;
                background-color: #10B981;
              }
              .stamp {
                position: relative;
                display: inline-block;
                color: #10B981;
                padding: 15px;
                border: 3px solid #10B981;
                border-radius: 10px;
                transform: rotate(-15deg);
                font-size: 20px;
                font-weight: bold;
                margin: 20px auto;
                text-align: center;
              }
            </style>
          </head>
          <body>
            <div class="receipt">
              <div class="header">
                <div class="school-name">School Name</div>
                <div class="receipt-title">Payment Receipt</div>
              </div>

              <div class="info-row">
                <span class="label">Receipt ID:</span>
                <span class="value">${payment.id.substring(0, 8).toUpperCase()}</span>
              </div>

              <div class="info-row">
                <span class="label">Student Name:</span>
                <span class="value">${childInfo.name}</span>
              </div>

              <div class="info-row">
                <span class="label">Class:</span>
                <span class="value">${childInfo.class}</span>
              </div>

              <div class="info-row">
                <span class="label">Parent Name:</span>
                <span class="value">${user?.name || 'Parent'}</span>
              </div>

              <div class="info-row">
                <span class="label">Date:</span>
                <span class="value">${formattedDate}</span>
              </div>

              <div class="info-row">
                <span class="label">UPI Reference:</span>
                <span class="value">${payment.upiReferenceId}</span>
              </div>

              <div class="amount">
                ₹${payment.amount.toLocaleString('en-IN')}
              </div>

              <div style="text-align: center;">
                <div class="stamp">PAID</div>
              </div>

              <div class="footer">
                <p>This is a computer-generated receipt and does not require a signature.</p>
                <p>For any queries, please contact the school administration.</p>
              </div>
            </div>
          </body>
        </html>
      `;

      // Generate PDF file
      const { uri } = await Print.printToFileAsync({ html });

      // Check if sharing is available
      if (Platform.OS === 'ios') {
        await Sharing.shareAsync(uri);
      } else if (Platform.OS === 'android') {
        const shareResult = await Share.share({
          url: uri,
          title: 'Payment Receipt',
          message: 'Here is your payment receipt',
        });

        if (shareResult.action === Share.sharedAction) {
          if (shareResult.activityType) {
            console.log('Shared with activity type:', shareResult.activityType);
          } else {
            console.log('Shared successfully');
          }
        } else if (shareResult.action === Share.dismissedAction) {
          console.log('Share dismissed');
        }
      } else {
        // Web platform
        Alert.alert('Receipt Generated', 'Receipt has been generated successfully.');
      }

    } catch (error) {
      console.error('Error generating receipt:', error);
      Alert.alert('Error', 'Failed to generate receipt. Please try again.');
    }
  };

  return (
    <View className="flex-1 bg-white">
      {/* Loading Indicator */}
      {loading && (
        <View className="absolute z-10 w-full h-full bg-black/30 items-center justify-center">
          <View className="bg-white p-4 rounded-xl shadow-lg">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="text-gray-700 mt-2 font-medium">Loading...</Text>
          </View>
        </View>
      )}

      {/* Header with Child Selection */}
      <View className="p-4 border-b border-gray-200">
        <TouchableOpacity
          className="flex-row items-center"
          onPress={() => setShowChildModal(true)}
          disabled={loading}
        >
          <View className="flex-row items-center">
            <View className="w-8 h-8 bg-blue-100 rounded-full items-center justify-center mr-2">
              <FontAwesome name="user" size={16} color="#3B82F6" />
            </View>
            <View>
              <Text className="text-lg font-semibold">
                {selectedChild ? selectedChild.name : 'All Children'}
              </Text>
              <View className="flex-row items-center">
                {selectedChild ? (
                  <Text className="text-gray-600 text-sm">
                    Class {selectedChild.class}
                  </Text>
                ) : (
                  <Text className="text-gray-600 text-sm">
                    Showing payments for all children
                  </Text>
                )}
                <FontAwesome name="chevron-down" size={12} color="#6b7280" style={{ marginLeft: 4 }} />
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </View>

      <ScrollView
        className="flex-1 p-4"
        contentContainerStyle={{ paddingBottom: 100 }} // Add padding to bottom to prevent content from being hidden behind tab bar
      >
        {/* UPI QR Code */}
        <View className="items-center mb-6">
          <Text className="text-lg font-semibold mb-2">School UPI QR Code</Text>
          <Image
            source={{ uri: paymentSettings.qrCodeUrl }}
            className="w-48 h-48"
          />
          <Text className="text-gray-600 mt-2">UPI ID: {paymentSettings.upiId}</Text>
          <Text className="text-xs text-gray-400 mt-1">
            Last updated: {new Date(paymentSettings.lastUpdated).toLocaleDateString()}
          </Text>
        </View>

        {/* Payment Form */}
        {selectedChild && (
          <View className="mb-6">
            <Text className="text-lg font-semibold mb-4">Submit Payment</Text>
            <TextInput
              className="p-3 border border-gray-300 rounded-lg mb-3"
              placeholder="Amount"
              value={amount}
              onChangeText={setAmount}
              keyboardType="numeric"
            />
            <TextInput
              className="p-3 border border-gray-300 rounded-lg mb-3"
              placeholder="UPI Reference ID"
              value={upiReferenceId}
              onChangeText={setUpiReferenceId}
            />
            <TouchableOpacity
              className="p-3 border border-gray-300 rounded-lg mb-3"
              onPress={handleSelectImage}
            >
              <Text className="text-gray-600">
                {screenshot ? 'Screenshot Selected' : 'Upload Payment Screenshot'}
              </Text>
            </TouchableOpacity>
            {screenshot && (
              <Image
                source={{ uri: screenshot }}
                className="w-32 h-32 mb-3"
              />
            )}
            {/* Pay Now Button - Opens UPI Apps */}
            <TouchableOpacity
              className="bg-green-500 p-3 rounded-lg mb-3"
              onPress={handlePayNow}
              disabled={loading}
            >
              <Text className="text-white text-center font-semibold">
                {loading ? 'Opening UPI Apps...' : 'Pay Now with UPI'}
              </Text>
            </TouchableOpacity>

            {/* Direct UPI App Selection */}
            <View className="flex-row flex-wrap justify-center mb-3">
              <Text className="text-gray-600 text-center w-full mb-2">Or select a UPI app:</Text>
              <TouchableOpacity
                className="bg-blue-500 p-2 rounded-lg m-1"
                onPress={() => {
                  const gpayUrl = `tez://upi/pay?pa=${paymentSettings.upiId}&pn=School&am=${amount}&cu=INR`;
                  Linking.openURL(gpayUrl).catch(() => Alert.alert('Error', 'Google Pay not installed'));
                }}
              >
                <Text className="text-white text-center">Google Pay</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="bg-purple-500 p-2 rounded-lg m-1"
                onPress={() => {
                  const phonepeUrl = `phonepe://pay?pa=${paymentSettings.upiId}&pn=School&am=${amount}&cu=INR`;
                  Linking.openURL(phonepeUrl).catch(() => Alert.alert('Error', 'PhonePe not installed'));
                }}
              >
                <Text className="text-white text-center">PhonePe</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="bg-blue-400 p-2 rounded-lg m-1"
                onPress={() => {
                  const paytmUrl = `paytmmp://pay?pa=${paymentSettings.upiId}&pn=School&am=${amount}&cu=INR`;
                  Linking.openURL(paytmUrl).catch(() => Alert.alert('Error', 'Paytm not installed'));
                }}
              >
                <Text className="text-white text-center">Paytm</Text>
              </TouchableOpacity>
            </View>

            {/* Submit Payment Button - After payment is done */}
            <TouchableOpacity
              className="bg-blue-500 p-3 rounded-lg"
              onPress={handleSubmitPayment}
            >
              <Text className="text-white text-center font-semibold">
                Submit Payment Details
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Payment History */}
        <View className="mt-8 mb-4">
          <View className="flex-row items-center mb-4">
            <View className="w-8 h-8 bg-green-500 rounded-full items-center justify-center mr-2">
              <FontAwesome name="history" size={16} color="white" />
            </View>
            <Text className="text-lg font-semibold">Payment History</Text>
          </View>

          {payments.length === 0 && !loading ? (
            <View className="items-center justify-center py-8 bg-white rounded-xl shadow-sm border border-gray-100">
              <View className="w-16 h-16 bg-gray-100 rounded-full items-center justify-center mb-2">
                <FontAwesome name="money" size={28} color="#9CA3AF" />
              </View>
              <Text className="text-gray-700 font-medium">No payment history</Text>
              <Text className="text-gray-500 text-sm text-center mt-1">Your payment records will appear here</Text>
            </View>
          ) : (
            payments.map((payment) => (
              <View
                key={payment.id}
                className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 mb-4"
              >
                <View className="flex-row justify-between items-start mb-3">
                  <View className="flex-row items-center">
                    <View className="w-12 h-12 bg-green-50 rounded-full items-center justify-center mr-3">
                      <Text className="text-green-600 font-bold text-lg">₹</Text>
                    </View>
                    <View>
                      <Text className="font-bold text-gray-800 text-lg">₹{payment.amount}</Text>
                      <View className="flex-row items-center">
                        <Text className="text-gray-500 text-xs">
                          {new Date(payment.date).toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' })}
                        </Text>
                        {payment.studentName && (
                          <>
                            <Text className="text-gray-400 mx-1 text-xs">•</Text>
                            <Text className="text-gray-500 text-xs">
                              {payment.studentName}
                            </Text>
                          </>
                        )}
                      </View>
                    </View>
                  </View>
                  <View
                    className="px-3 py-1 rounded-full"
                    style={{ backgroundColor: getStatusColor(payment.status) }}
                  >
                    <Text className="text-white text-xs font-medium capitalize">
                      {payment.status}
                    </Text>
                  </View>
                </View>
                <View className="bg-gray-50 p-3 rounded-lg mb-2">
                  <Text className="text-gray-700 font-medium mb-1">
                    UPI Reference ID
                  </Text>
                  <Text className="text-gray-600 font-mono">
                    {payment.upiReferenceId}
                  </Text>
                </View>
                {payment.status === 'approved' && (
                  <TouchableOpacity
                    className="mt-3 p-3 bg-green-100 rounded-lg flex-row items-center justify-center"
                    onPress={() => generateReceipt(payment)}
                  >
                    <FontAwesome name="download" size={16} color="#10B981" style={{ marginRight: 8 }} />
                    <Text className="text-green-700 font-medium">
                      Download Receipt
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            ))
          )}
        </View>
      </ScrollView>

      {/* Child Selection Modal */}
      <Modal
        visible={showChildModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowChildModal(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center">
          <View className="bg-white m-4 p-4 rounded-lg">
            <Text className="text-xl font-bold mb-4">Select Child</Text>
            <TouchableOpacity
              className="p-4 border-b border-gray-200 mb-2 bg-blue-50"
              onPress={() => {
                setSelectedChild(null);
                setShowChildModal(false);
                // Clear the saved child ID
                AsyncStorage.removeItem('selectedChildId');
              }}
            >
              <Text className="text-lg text-blue-600 font-medium">View All Payments</Text>
              <Text className="text-blue-500 text-sm">Show payments for all children</Text>
            </TouchableOpacity>

            {children.length === 0 ? (
              <View className="items-center justify-center py-4">
                <Text className="text-gray-500 text-center">
                  No children found. Please contact the school administration.
                </Text>
              </View>
            ) : (
              children.map((child) => (
                <TouchableOpacity
                  key={child.id}
                  className="p-4 border-b border-gray-200"
                  onPress={() => handleSelectChild(child)}
                >
                  <Text className="text-lg">{child.name}</Text>
                  <Text className="text-gray-600">Class {child.class}</Text>
                </TouchableOpacity>
              ))
            )}
            <TouchableOpacity
              className="mt-4 p-2 bg-gray-200 rounded-lg"
              onPress={() => setShowChildModal(false)}
            >
              <Text className="text-center">Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}