import { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../../hooks/useAuth';
import { FontAwesome } from '@expo/vector-icons';

type UserRole = 'parent' | 'teacher';

export default function RegisterScreen() {
  const router = useRouter();
  const { signUp } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [role, setRole] = useState<UserRole>('parent');
  const [loading, setLoading] = useState(false);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePassword = (password: string) => {
    return password.length >= 6;
  };

  const handleRegister = async () => {
    if (!email || !password || !name) {
      Alert.alert('Missing Information', 'Please fill in all fields');
      return;
    }

    if (!validateEmail(email)) {
      Alert.alert('Invalid Email', 'Please enter a valid email address');
      return;
    }

    if (!validatePassword(password)) {
      Alert.alert('Weak Password', 'Password must be at least 6 characters long');
      return;
    }

    try {
      setLoading(true);
      await signUp(email, password, name, role);
      // Navigation will be handled by the auth state change in useAuth
    } catch (error: any) {
      Alert.alert('Registration Failed', error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View className="flex-1 bg-white px-6 py-8">
      <View className="flex-1 justify-center">
        <View className="mb-8">
          <Text className="text-3xl font-bold text-gray-900 text-center">Welcome!</Text>
          <Text className="text-base text-gray-600 text-center mt-2">Create your account to get started</Text>
        </View>

        <View className="space-y-5">
          <View>
            <View className="flex-row items-center mb-2">
              <FontAwesome name="user" size={16} color="#6B7280" />
              <Text className="text-gray-600 ml-2">Full Name</Text>
            </View>
            <TextInput
              className="w-full h-12 px-4 border border-gray-300 rounded-xl bg-gray-50"
              placeholder="Enter your full name"
              value={name}
              onChangeText={setName}
              placeholderTextColor="#9CA3AF"
            />
          </View>

          <View>
            <View className="flex-row items-center mb-2">
              <FontAwesome name="envelope" size={16} color="#6B7280" />
              <Text className="text-gray-600 ml-2">Email</Text>
            </View>
            <TextInput
              className="w-full h-12 px-4 border border-gray-300 rounded-xl bg-gray-50"
              placeholder="Enter your email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              placeholderTextColor="#9CA3AF"
            />
          </View>

          <View>
            <View className="flex-row items-center mb-2">
              <FontAwesome name="lock" size={16} color="#6B7280" />
              <Text className="text-gray-600 ml-2">Password</Text>
            </View>
            <TextInput
              className="w-full h-12 px-4 border border-gray-300 rounded-xl bg-gray-50"
              placeholder="Create a password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              placeholderTextColor="#9CA3AF"
            />
          </View>

          <View>
            <Text className="text-gray-600 mb-2">I am a:</Text>
            <View className="flex-row space-x-4">
              {(['parent', 'teacher'] as UserRole[]).map((r) => (
                <TouchableOpacity
                  key={r}
                  className={`flex-1 h-14 rounded-xl justify-center items-center border-2 ${
                    role === r 
                      ? 'bg-blue-50 border-blue-500' 
                      : 'bg-white border-gray-200'
                  }`}
                  onPress={() => setRole(r)}
                  activeOpacity={0.7}
                >
                  <FontAwesome 
                    name={r === 'parent' ? 'home' : 'book'} 
                    size={18} 
                    color={role === r ? '#3B82F6' : '#6B7280'} 
                  />
                  <Text
                    className={`text-base font-medium mt-1 ${
                      role === r ? 'text-blue-500' : 'text-gray-600'
                    }`}
                  >
                    {r.charAt(0).toUpperCase() + r.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <TouchableOpacity
            className={`w-full h-12 rounded-xl justify-center items-center mt-6 ${
              loading ? 'bg-blue-400' : 'bg-blue-500'
            }`}
            onPress={handleRegister}
            disabled={loading}
            activeOpacity={0.7}
          >
            {loading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text className="text-white font-semibold text-base">
                Create Account
              </Text>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => router.push('/(auth)/login')}
            className="mt-4 py-2"
            activeOpacity={0.7}
          >
            <Text className="text-blue-500 text-center text-base">
              Already have an account? <Text className="font-semibold">Sign In</Text>
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
} 