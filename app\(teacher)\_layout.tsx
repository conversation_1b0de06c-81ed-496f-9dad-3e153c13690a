import { Tabs } from 'expo-router';
import { useAuth } from '../../hooks/useAuth';
import { FontAwesome } from '@expo/vector-icons';
import { useWindowDimensions } from 'react-native';

interface TabBarIconProps {
  color: string;
  size: number;
  focused?: boolean;
}

export default function TeacherLayout() {
  const { user } = useAuth();
  const { width } = useWindowDimensions();
  const isMobile = width < 768;

  if (!user || user.role !== 'teacher') {
    return null;
  }

  return (
    <Tabs
      sceneContainerStyle={{
        paddingBottom: 100, // Add padding to the bottom of all screens
        backgroundColor: '#f9fafb',
      }}
      screenOptions={{
        tabBarActiveTintColor: '#3b82f6',
        tabBarInactiveTintColor: '#6b7280',
        headerShown: false,
        tabBarStyle: {
          height: 90,
          paddingBottom: 15,
          paddingTop: 15,
          borderTopWidth: 0,
          elevation: 10,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -3 },
          shadowOpacity: 0.15,
          shadowRadius: 5,
          backgroundColor: 'white',
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          borderTopLeftRadius: 25,
          borderTopRightRadius: 25,
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: 0,
        },
        tabBarItemStyle: {
          paddingVertical: 10,
          height: 80,
          width: 65,
          justifyContent: 'center',
          alignItems: 'center',
          marginHorizontal: 5,
        },
        tabBarLabelPosition: 'below-icon',
        tabBarAllowFontScaling: false,
        tabBarLabelStyle: {
          fontSize: isMobile ? 10 : 12,
          marginTop: 2,
          fontWeight: '600',
          opacity: 1,
          color: '#6b7280',
          display: 'flex',
          textAlign: 'center',
        },
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: 'Home',
          tabBarLabel: 'Home',
          tabBarIcon: ({ color, size }: TabBarIconProps) => (
            <FontAwesome name="home" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="attendance"
        options={{
          title: 'Attendance',
          tabBarLabel: 'Attendance',
          tabBarIcon: ({ color, size }: TabBarIconProps) => (
            <FontAwesome name="calendar-check-o" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="calendar"
        options={{
          title: 'Calendar',
          tabBarLabel: 'Calendar',
          tabBarIcon: ({ color, size }: TabBarIconProps) => (
            <FontAwesome name="calendar" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarLabel: 'Profile',
          tabBarIcon: ({ color, size }: TabBarIconProps) => (
            <FontAwesome name="user" size={size} color={color} />
          ),
        }}
      />
      {/* Hidden screens - accessed via navigation but not shown in tabs */}
      <Tabs.Screen
        name="students"
        options={{
          href: null,
        }}
      />
    </Tabs>
  );
}