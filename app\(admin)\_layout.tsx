import { Tabs } from 'expo-router';
import { FontAwesome } from '@expo/vector-icons';
import { useWindowDimensions, View } from 'react-native';

interface TabBarIconProps {
  color: string;
  size: number;
  focused?: boolean;
}

export default function AdminLayout() {
  const { width } = useWindowDimensions();
  const isMobile = width < 768;

  return (
    <Tabs
      screenOptions={({ route }) => ({
        tabBarActiveTintColor: '#2563eb',
        tabBarInactiveTintColor: '#94a3b8',
        headerShown: false,
        tabBarStyle: {
          height: isMobile ? 80 : 90,
          paddingBottom: isMobile ? 10 : 16,
          paddingTop: isMobile ? 10 : 16,
          borderTopWidth: 0,
          elevation: 15,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -4 },
          shadowOpacity: 0.18,
          shadowRadius: 10,
          backgroundColor: '#fff',
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          borderTopLeftRadius: 22,
          borderTopRightRadius: 22,
          justifyContent: 'space-evenly',
          alignItems: 'center',
          marginHorizontal: 0,
        },
        tabBarLabelStyle: {
          fontSize: isMobile ? 13 : 15,
          marginTop: 4,
          fontWeight: '700',
        },
        tabBarIconStyle: {
          marginBottom: 0,
        },
        tabBarItemStyle: {
          paddingVertical: 6,
          height: isMobile ? 70 : 80,
          width: isMobile ? 80 : 110,
          justifyContent: 'center',
          alignItems: 'center',
          marginHorizontal: isMobile ? 2 : 8,
          borderRadius: 16,
        },
        tabBarAllowFontScaling: false,
        tabBarLabelPosition: 'below-icon',
        tabBarIcon: ({ color, focused }: TabBarIconProps) => {
          let iconName: any;
          if (route.name === 'dashboard') iconName = 'dashboard';
          else if (route.name === 'students') iconName = 'graduation-cap';
          else if (route.name === 'calendar') iconName = 'calendar';
          else if (route.name === 'more') iconName = 'ellipsis-h';
          return (
            <View
              style={focused ? {
                backgroundColor: '#e0e7ff',
                borderRadius: 16,
                padding: 10,
                shadowColor: '#2563eb',
                shadowOpacity: 0.15,
                shadowRadius: 8,
                shadowOffset: { width: 0, height: 2 },
              } : {}}
            >
              <FontAwesome
                name={iconName}
                size={isMobile ? 28 : 32}
                color={color}
                style={{ fontWeight: focused ? 'bold' : 'normal' }}
              />
            </View>
          );
        },
      })}
      sceneContainerStyle={{ backgroundColor: '#f9fafb' }}
    >
      <Tabs.Screen
        name="dashboard"
        options={{
          title: 'Dashboard',
        }}
      />
      <Tabs.Screen
        name="students"
        options={{
          title: 'Students',
        }}
      />
      <Tabs.Screen
        name="calendar"
        options={{
          title: 'Calendar',
        }}
      />
      <Tabs.Screen
        name="more"
        options={{
          title: 'More',
        }}
      />
      {/* Hide the _screens folder from tabs */}
      <Tabs.Screen
        name="_screens"
        options={{
          href: null,
          tabBarStyle: { display: 'none' },
        }}
      />
      {/* Hide the index from tabs */}
      <Tabs.Screen
        name="index"
        options={{
          href: null,
          tabBarStyle: { display: 'none' },
        }}
      />
    </Tabs>
  );
}