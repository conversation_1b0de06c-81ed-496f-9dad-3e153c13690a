import { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { useAuth } from '../../hooks/useAuth';
import { FontAwesome } from '@expo/vector-icons';
import { router } from 'expo-router';
import { firestore, auth } from '../../config/firebase';
import { signOut as firebaseSignOut } from 'firebase/auth';
import { doc, updateDoc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';

interface Child {
  id: string;
  name: string;
  class: string;
  rollNumber?: string;
  admissionDate?: string;
  status?: 'active' | 'inactive';
  address?: string;
  parentName?: string;
  parentEmail?: string;
  parentPhone?: string;
}

export default function ParentProfile() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [children, setChildren] = useState<Child[]>([]);
  // We use studentDetails instead of selectedChild
  const [showStudentModal, setShowStudentModal] = useState(false);
  const [studentDetails, setStudentDetails] = useState<Child | null>(null);
  const [loadingStudentDetails, setLoadingStudentDetails] = useState(false);

  // Initialize data loading when component mounts
  useEffect(() => {
    if (user) {
      // Set initial values from auth context to prevent flicker
      setName(user.name || '');
      setEmail(user.email || '');
      setPhone(user.phone || '');

      // Set loading state immediately
      setLoading(true);

      // Small delay to ensure loading state is applied
      setTimeout(() => {
        fetchUserDetails();
      }, 100);
    }
  }, [user]);

  const fetchUserDetails = async () => {
    if (!user) return;

    try {
      // Loading state is already set in useEffect
      console.log('Fetching user details for:', user.id);

      // Fetch user data and children in parallel for better performance
      const userRef = doc(firestore, 'users', user.id);
      const userDocPromise = getDoc(userRef);

      // Start children query if we have a phone number from auth context
      let childrenQueryPromise = null;
      if (user.phone) {
        console.log('Starting children query with phone:', user.phone);
        const childrenCollection = collection(firestore, 'students');
        const childrenQuery = query(childrenCollection, where('parentPhone', '==', user.phone));
        childrenQueryPromise = getDocs(childrenQuery);
      }

      // Wait for user data
      const userDoc = await userDocPromise;
      let userPhone = user.phone || '';

      if (userDoc.exists()) {
        const userData = userDoc.data();
        setName(userData.name || '');
        setEmail(userData.email || '');
        setPhone(userData.phone || '');
        userPhone = userData.phone || '';

        // If we didn't start children query yet but now have a phone number
        if (!childrenQueryPromise && userPhone) {
          console.log('Starting children query after user data with phone:', userPhone);
          const childrenCollection = collection(firestore, 'students');
          const childrenQuery = query(childrenCollection, where('parentPhone', '==', userPhone));
          childrenQueryPromise = getDocs(childrenQuery);
        }
      }

      // Fetch children based on parent's phone number
      if (childrenQueryPromise) {
        const querySnapshot = await childrenQueryPromise;
        const childrenData: Child[] = [];

        querySnapshot.forEach((docSnapshot: any) => {
          const data = docSnapshot.data();
          childrenData.push({
            id: docSnapshot.id,
            name: data.name || 'Unknown',
            class: data.class || 'Unknown',
          });
        });

        console.log(`Found ${childrenData.length} children for this parent`);
        setChildren(childrenData);
      } else {
        console.log('No phone number found, cannot fetch children');
        setChildren([]);
      }
    } catch (error) {
      console.error('Error fetching user details:', error);
      Alert.alert('Error', 'Failed to load user details. Please try again.');
    } finally {
      // Add a small delay before removing loading state for smoother transition
      setTimeout(() => {
        setLoading(false);
      }, 300);
    }
  };

  const handleChangePassword = async () => {
    if (newPassword !== confirmPassword) {
      Alert.alert('Error', 'New passwords do not match');
      return;
    }

    try {
      // Here you would implement the password change logic with Firebase
      Alert.alert('Success', 'Password changed successfully');
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } catch (error) {
      Alert.alert('Error', 'Failed to change password');
    }
  };

  // State for logout loading
  const [loggingOut, setLoggingOut] = useState(false);

  const handleLogout = async () => {
    try {
      // Use separate loading state for logout to avoid full screen overlay
      setLoggingOut(true);
      console.log('Sign out initiated');

      // Small delay to ensure the button loading state is visible
      await new Promise(resolve => setTimeout(resolve, 300));

      // Use Firebase's signOut function directly
      await firebaseSignOut(auth);
      console.log('Firebase signOut completed successfully');

      // Force navigation to login page
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Sign out error:', error);
      Alert.alert('Error', 'Failed to sign out. Please try again.');
      setLoggingOut(false); // Reset loading state on error
    }
    // Note: We don't reset loggingOut on success as we're navigating away
  };

  const fetchStudentDetails = async (studentId: string) => {
    try {
      setLoadingStudentDetails(true);
      console.log('Fetching detailed information for student:', studentId);

      const studentRef = doc(firestore, 'students', studentId);
      const studentDoc = await getDoc(studentRef);

      if (studentDoc.exists()) {
        const data = studentDoc.data();
        const detailedStudent: Child = {
          id: studentDoc.id,
          name: data.name || 'Unknown',
          class: data.class || 'Unknown',
          rollNumber: data.rollNumber || '',
          admissionDate: data.admissionDate || '',
          status: data.status || 'active',
          address: data.address || '',
          parentName: data.parentName || '',
          parentEmail: data.parentEmail || '',
          parentPhone: data.parentPhone || '',
        };

        console.log('Student details fetched successfully');
        setStudentDetails(detailedStudent);
        setShowStudentModal(true);
      } else {
        console.log('Student document not found');
        Alert.alert('Error', 'Student information not found');
      }
    } catch (error) {
      console.error('Error fetching student details:', error);
      Alert.alert('Error', 'Failed to load student details');
    } finally {
      setLoadingStudentDetails(false);
    }
  };

  const handleViewStudentDetails = (child: Child) => {
    fetchStudentDetails(child.id);
  };

  const handleSave = async () => {
    if (!user) return;

    try {
      setSaving(true);
      const userRef = doc(firestore, 'users', user.id);

      await updateDoc(userRef, {
        name,
        phone,
      });

      Alert.alert('Success', 'Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <ScrollView
      className="flex-1 bg-white p-4"
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      {/* Loading Indicator */}
      {loading && (
        <View
          className="absolute z-10 w-full h-full items-center justify-center"
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }}
        >
          <View className="bg-white p-5 rounded-xl shadow-lg border border-gray-100">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="text-gray-700 mt-3 font-medium text-center">Loading your profile...</Text>
            <Text className="text-gray-500 text-xs mt-1 text-center">This may take a moment</Text>
          </View>
        </View>
      )}

      <Text className="text-2xl font-bold mb-6">Profile</Text>

      {/* Profile Section */}
      <View className="mb-6">
        <View className="items-center mb-6">
          <View className="w-24 h-24 bg-blue-100 rounded-full items-center justify-center mb-2">
            <FontAwesome name="user" size={40} color="#3B82F6" />
          </View>
          <Text className="text-xl font-bold">{name || 'Parent'}</Text>
          <Text className="text-gray-500">{email}</Text>
        </View>

        <Text className="text-lg font-semibold mb-4">Personal Information</Text>
        <View className="space-y-4">
          <View>
            <Text className="text-sm font-medium text-gray-700 mb-1">Name</Text>
            <TextInput
              className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900"
              placeholder="Enter your name"
              value={name}
              onChangeText={setName}
              placeholderTextColor="#9CA3AF"
            />
          </View>

          <View>
            <Text className="text-sm font-medium text-gray-700 mb-1">Email</Text>
            <TextInput
              className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900"
              placeholder="Enter your email"
              value={email}
              editable={false}
              placeholderTextColor="#9CA3AF"
            />
            <Text className="text-xs text-gray-500 mt-1">Email cannot be changed</Text>
          </View>

          <View>
            <Text className="text-sm font-medium text-gray-700 mb-1">Phone Number</Text>
            <TextInput
              className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900"
              placeholder="Enter your phone number"
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
              placeholderTextColor="#9CA3AF"
            />
            <Text className="text-xs text-gray-500 mt-1">
              This phone number will be used to link your children to your account
            </Text>
          </View>

          <TouchableOpacity
            className="w-full bg-blue-500 p-4 rounded-xl mt-2 opacity-90 active:opacity-100"
            onPress={handleSave}
            disabled={saving}
          >
            {saving ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text className="text-white text-center font-semibold">Save Changes</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>

      {/* Change Password Section */}
      {/* <View className="mb-6">
        <Text className="text-lg font-semibold mb-4">Change Password</Text>
        <View className="space-y-4">
          <TextInput
            className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900"
            placeholder="Current Password"
            value={currentPassword}
            onChangeText={setCurrentPassword}
            secureTextEntry
            placeholderTextColor="#9CA3AF"
          />
          <TextInput
            className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900"
            placeholder="New Password"
            value={newPassword}
            onChangeText={setNewPassword}
            secureTextEntry
            placeholderTextColor="#9CA3AF"
          />
          <TextInput
            className="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-900"
            placeholder="Confirm New Password"
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry
            placeholderTextColor="#9CA3AF"
          />
          <TouchableOpacity
            className="w-full bg-blue-500 p-4 rounded-xl opacity-90 active:opacity-100"
            onPress={handleChangePassword}
          >
            <Text className="text-white text-center font-semibold">
              Change Password
            </Text>
          </TouchableOpacity>
        </View>
      </View> */}

      {/* Child Selection Section */}
      <View className="mb-6">
        <Text className="text-lg font-semibold mb-4">Your Children</Text>
        <View className="space-y-2">
          {children.length > 0 ? (
            children.map((child) => (
              <TouchableOpacity
                key={child.id}
                className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm active:bg-gray-50"
                onPress={() => handleViewStudentDetails(child)}
              >
                <View className="flex-row items-center">
                  <View className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center mr-3">
                    <FontAwesome name="user-circle" size={18} color="#3B82F6" />
                  </View>
                  <View className="flex-1">
                    <Text className="font-bold text-gray-800">{child.name}</Text>
                    <Text className="text-gray-600">Class {child.class}</Text>
                  </View>
                  <FontAwesome name="chevron-right" size={16} color="#9CA3AF" />
                </View>
              </TouchableOpacity>
            ))
          ) : (
            <View className="p-6 border border-gray-200 rounded-lg bg-gray-50 items-center">
              <FontAwesome name="child" size={30} color="#9CA3AF" style={{ marginBottom: 10 }} />
              <Text className="text-center text-gray-500 mb-1">
                No children found
              </Text>
              <Text className="text-center text-gray-400 text-sm">
                Please update your phone number to match the phone number used when registering your children.
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Student Details Modal */}
      <Modal
        visible={showStudentModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowStudentModal(false)}
      >
        <View className="flex-1 bg-black/50 justify-end">
          <View className="bg-white rounded-t-3xl max-h-[90%]">
            <View className="p-4 border-b border-gray-200 flex-row justify-between items-center">
              <Text className="text-xl font-bold text-gray-800">Student Details</Text>
              <TouchableOpacity
                onPress={() => setShowStudentModal(false)}
                className="p-2"
              >
                <FontAwesome name="close" size={20} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <ScrollView className="p-4" contentContainerStyle={{ paddingBottom: 30 }}>
              {loadingStudentDetails ? (
                <View className="items-center justify-center py-10">
                  <ActivityIndicator size="large" color="#3B82F6" />
                  <Text className="text-gray-500 mt-4">Loading student details...</Text>
                </View>
              ) : studentDetails ? (
                <View className="space-y-6">
                  {/* Student Basic Info */}
                  <View className="items-center mb-2">
                    <View className="w-20 h-20 bg-blue-100 rounded-full items-center justify-center mb-3">
                      <FontAwesome name="user" size={32} color="#3B82F6" />
                    </View>
                    <Text className="text-xl font-bold text-gray-800">{studentDetails.name}</Text>
                    <View className="flex-row items-center mt-1">
                      <Text className="text-gray-600">Class {studentDetails.class}</Text>
                      {studentDetails.status && (
                        <View className={`ml-2 px-2 py-0.5 rounded-full ${studentDetails.status === 'active' ? 'bg-green-100' : 'bg-red-100'}`}>
                          <Text className={`text-xs font-medium ${studentDetails.status === 'active' ? 'text-green-800' : 'text-red-800'}`}>
                            {studentDetails.status}
                          </Text>
                        </View>
                      )}
                    </View>
                  </View>

                  {/* Academic Information */}
                  <View className="bg-gray-50 p-4 rounded-xl">
                    <Text className="text-base font-bold text-gray-800 mb-3">Academic Information</Text>

                    <View className="space-y-3">
                      <View className="flex-row">
                        <Text className="text-gray-500 w-32">Roll Number:</Text>
                        <Text className="text-gray-800 font-medium flex-1">{studentDetails.rollNumber || 'Not assigned'}</Text>
                      </View>

                      <View className="flex-row">
                        <Text className="text-gray-500 w-32">Class:</Text>
                        <Text className="text-gray-800 font-medium flex-1">{studentDetails.class}</Text>
                      </View>

                      <View className="flex-row">
                        <Text className="text-gray-500 w-32">Admission Date:</Text>
                        <Text className="text-gray-800 font-medium flex-1">
                          {studentDetails.admissionDate ? new Date(studentDetails.admissionDate).toLocaleDateString() : 'Not available'}
                        </Text>
                      </View>
                    </View>
                  </View>

                  {/* Contact Information */}
                  <View className="bg-gray-50 p-4 rounded-xl">
                    <Text className="text-base font-bold text-gray-800 mb-3">Contact Information</Text>

                    <View className="space-y-3">
                      <View className="flex-row">
                        <Text className="text-gray-500 w-32">Parent Name:</Text>
                        <Text className="text-gray-800 font-medium flex-1">{studentDetails.parentName || 'Not available'}</Text>
                      </View>

                      <View className="flex-row">
                        <Text className="text-gray-500 w-32">Phone Number:</Text>
                        <Text className="text-gray-800 font-medium flex-1">{studentDetails.parentPhone || 'Not available'}</Text>
                      </View>

                      <View className="flex-row">
                        <Text className="text-gray-500 w-32">Email:</Text>
                        <Text className="text-gray-800 font-medium flex-1">{studentDetails.parentEmail || 'Not available'}</Text>
                      </View>

                      {studentDetails.address && (
                        <View className="flex-row">
                          <Text className="text-gray-500 w-32">Address:</Text>
                          <Text className="text-gray-800 font-medium flex-1">{studentDetails.address}</Text>
                        </View>
                      )}
                    </View>
                  </View>

                  {/* Actions */}
                  <TouchableOpacity
                    className="bg-blue-500 p-4 rounded-xl mt-4"
                    onPress={() => setShowStudentModal(false)}
                  >
                    <Text className="text-white font-bold text-center">Close</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View className="items-center justify-center py-10">
                  <FontAwesome name="exclamation-circle" size={40} color="#9CA3AF" />
                  <Text className="text-gray-500 mt-4 text-center">Student information not available</Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Logout Button */}
      <View className="mt-6 mb-16">
        <TouchableOpacity
          className={`w-full ${loggingOut ? 'bg-red-400' : 'bg-red-500'} p-5 rounded-xl opacity-90 active:opacity-100`}
          onPress={handleLogout}
          disabled={loggingOut}
          style={{
            shadowColor: '#EF4444',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.3,
            shadowRadius: 5,
            elevation: 4
          }}
        >
          <View className="flex-row justify-center items-center">
            {loggingOut ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <>
                <FontAwesome name="sign-out" size={20} color="white" style={{ marginRight: 10 }} />
                <Text className="text-white text-center font-bold text-base">Sign Out</Text>
              </>
            )}
          </View>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}
