import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useAuth } from '../../hooks/useAuth';
import { useEffect, useState } from 'react';
import { useRouter } from 'expo-router';

export default function PendingApprovalScreen() {
  const { user, refreshUserStatus } = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  const [lastRefreshed, setLastRefreshed] = useState(new Date());
  const router = useRouter();

  // Function to handle manual refresh
  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      await refreshUserStatus();
      setLastRefreshed(new Date());
    } catch (error) {
      console.error('Error refreshing status:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Auto-refresh on mount and periodically
  useEffect(() => {
    // Refresh immediately when component mounts
    handleRefresh();

    // Set up interval to refresh every 30 seconds
    const intervalId = setInterval(() => {
      handleRefresh();
    }, 30000); // 30 seconds

    return () => clearInterval(intervalId);
  }, []);

  // Redirect if user is approved
  useEffect(() => {
    if (user && user.status === 'approved') {
      console.log('User is approved, redirecting...');
      // Determine target path based on role
      let targetPath;
      switch (user.role) {
        case 'admin':
          targetPath = '/(admin)/dashboard';
          break;
        case 'parent':
          targetPath = '/(parent)/home';
          break;
        case 'teacher':
          targetPath = '/(teacher)/home';
          break;
        default:
          targetPath = '/(auth)/login';
      }
      router.replace(targetPath);
    }
  }, [user]);

  return (
    <View className="flex-1 bg-white items-center justify-center p-6">
      <View className="items-center">
        <View className="w-24 h-24 bg-blue-100 rounded-full items-center justify-center mb-6">
          <FontAwesome name="clock-o" size={48} color="#3B82F6" />
        </View>

        <Text className="text-2xl font-bold text-gray-800 mb-2 text-center">
          Pending Approval
        </Text>

        <Text className="text-base text-gray-600 text-center mb-8">
          Your registration is under review by the school administration. You will be notified once your account is approved.
        </Text>

        <View className="bg-blue-50 p-4 rounded-xl w-full mb-6">
          <Text className="text-sm text-gray-600 text-center">
            For any queries, please contact the school administration at:
          </Text>
          <Text className="text-sm text-blue-600 text-center mt-2">
            <EMAIL>
          </Text>
        </View>

        {/* Refresh button */}
        <TouchableOpacity
          className="flex-row items-center bg-blue-500 px-4 py-2 rounded-lg mt-4"
          onPress={handleRefresh}
          disabled={refreshing}
        >
          {refreshing ? (
            <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
          ) : (
            <FontAwesome name="refresh" size={16} color="white" style={{ marginRight: 8 }} />
          )}
          <Text className="text-white font-medium">
            {refreshing ? 'Checking status...' : 'Check approval status'}
          </Text>
        </TouchableOpacity>

        <Text className="text-xs text-gray-500 mt-2">
          Last checked: {lastRefreshed.toLocaleTimeString()}
        </Text>
      </View>
    </View>
  );
}