import { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  ActivityIndicator,
  Switch,
} from 'react-native';
import { router } from 'expo-router';

import { FontAwesome } from '@expo/vector-icons';
import { useAuth } from '../../../hooks/useAuth';
import { firestore, auth } from '../../../config/firebase';
import {
  collection,
  getDocs,
  doc,
  deleteDoc,
  query,
  orderBy,
  where,
} from 'firebase/firestore';
import {
  createUserWithEmailAndPassword,
} from 'firebase/auth';
import { setDoc } from 'firebase/firestore';

type UserRole = 'admin' | 'parent' | 'teacher';

interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  phone?: string;
  password?: string;
}

export default function UserManagement() {
  const { signIn } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showTeacherDetails, setShowTeacherDetails] = useState(false);
  const [selectedTeacherDetails, setSelectedTeacherDetails] = useState<any>(null);

  // Filter states
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('newest');

  // New user form state
  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    name: '',
    role: 'parent' as UserRole,
    autoApprove: true,
    phone: '',
    // Teacher professional details
    subject: '',
    classTeacher: '',
    qualification: '',
    experience: '',
    joiningDate: new Date().toISOString().split('T')[0],
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    if (users.length > 0) {
      filterUsers();
    }
  }, [searchQuery, roleFilter, statusFilter, sortBy, users]);

  // Teacher-specific fields have been removed

  // Function to handle logging in as the newly created user
  const handleLoginAsUser = async (credentials: { email: string; password: string; role: UserRole }) => {
    try {
      // Confirm with the admin before proceeding
      Alert.alert(
        'Confirm Login',
        `You are about to log in as ${credentials.email} with role: ${credentials.role}. Your current admin session will end.`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Continue',
            onPress: async () => {
              try {
                setLoading(true);
                // Sign in with the new user credentials
                await signIn(credentials.email, credentials.password);
                // The app's _layout.tsx will automatically redirect based on the user's role
              } catch (error: any) {
                console.error('Error logging in as new user:', error);
                Alert.alert('Login Failed', error.message || 'Failed to log in as the new user');
              } finally {
                setLoading(false);
              }
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error in handleLoginAsUser:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    }
  };

  const fetchUsers = async () => {
    try {
      setLoading(true);
      console.log('Fetching users from Firestore...');

      const usersCollection = collection(firestore, 'users');
      const usersQuery = query(usersCollection, orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(usersQuery);

      const usersData: User[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        usersData.push({
          id: doc.id,
          email: data.email || '',
          name: data.name || '',
          role: data.role || 'parent',
          status: data.status || 'pending',
          createdAt: data.createdAt ? new Date(data.createdAt.toDate()).toISOString() : new Date().toISOString(),
          phone: data.phone || '',
          password: data.password || '',
        });
      });

      console.log(`Found ${usersData.length} users`);
      setUsers(usersData);
      setFilteredUsers(usersData);
    } catch (error) {
      console.error('Error fetching users:', error);
      Alert.alert('Error', 'Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  // Function to reset all filters
  const resetFilters = () => {
    setSearchQuery('');
    setRoleFilter('all');
    setStatusFilter('all');
    setSortBy('newest');
  };

  const filterUsers = () => {
    let filtered = [...users];

    // Apply search query filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (user) =>
          user.name.toLowerCase().includes(query) ||
          user.email.toLowerCase().includes(query) ||
          user.role.toLowerCase().includes(query)
      );
    }

    // Apply role filter
    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === roleFilter);
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(user => user.status === statusFilter);
    }

    // Apply sorting
    if (sortBy === 'newest') {
      filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    } else if (sortBy === 'oldest') {
      filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
    } else if (sortBy === 'name') {
      filtered.sort((a, b) => a.name.localeCompare(b.name));
    }

    setFilteredUsers(filtered);
  };

  const handleCreateUser = async () => {
    if (!newUser.email || !newUser.password || !newUser.name) {
      Alert.alert('Error', 'Please fill all required fields');
      return;
    }

    try {
      setIsSubmitting(true);

      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        newUser.email,
        newUser.password
      );

      const uid = userCredential.user.uid;

      // Prepare user data based on role
      const userData = {
        email: newUser.email,
        name: newUser.name,
        role: newUser.role,
        status: newUser.autoApprove ? 'approved' : 'pending',
        createdAt: new Date(),
        phone: newUser.phone || '',
        password: newUser.password, // Store password for admin reference
      };

      // Create a record in the teachers collection if role is teacher
      if (newUser.role === 'teacher') {
        await setDoc(doc(firestore, 'teachers', uid), {
          name: newUser.name,
          email: newUser.email,
          phone: newUser.phone || '',
          userId: uid,
          createdAt: new Date(),
          // Add teacher professional details
          subject: newUser.subject || '',
          classTeacher: newUser.classTeacher || '',
          qualification: newUser.qualification || '',
          experience: newUser.experience || '',
          joiningDate: newUser.joiningDate || new Date().toISOString().split('T')[0],
        });
      }

      // Add user data to Firestore users collection
      await setDoc(doc(firestore, 'users', uid), userData);

      // Store the created user credentials for potential login
      const createdUserCredentials = {
        email: newUser.email,
        password: newUser.password,
        name: newUser.name,
        role: newUser.role
      };

      // Show success alert with option to login as the created user
      Alert.alert(
        'Success',
        `User ${newUser.name} created successfully`,
        [
          { text: 'OK', style: 'default' },
          {
            text: 'Login as this user',
            onPress: () => handleLoginAsUser(createdUserCredentials),
            style: 'default'
          }
        ]
      );

      // Reset form and close modal
      setNewUser({
        email: '',
        password: '',
        name: '',
        role: 'parent',
        autoApprove: true,
        phone: '',
        // Reset teacher professional details
        subject: '',
        classTeacher: '',
        qualification: '',
        experience: '',
        joiningDate: new Date().toISOString().split('T')[0],
      });

      setShowAddModal(false);

      // Refresh user list
      fetchUsers();
    } catch (error: any) {
      console.error('Error creating user:', error);
      Alert.alert('Error', error.message || 'Failed to create user');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    try {
      setIsSubmitting(true);

      // Delete user from Firestore
      await deleteDoc(doc(firestore, 'users', selectedUser.id));

      // Note: Deleting the actual Firebase Auth user requires admin SDK
      // which cannot be used directly in client apps for security reasons
      // In a production app, this would be handled by a Cloud Function

      Alert.alert('Success', `User ${selectedUser.name} deleted successfully`);

      // Refresh user list
      fetchUsers();
      setSelectedUser(null);
      setShowDeleteConfirm(false);
    } catch (error) {
      console.error('Error deleting user:', error);
      Alert.alert('Error', 'Failed to delete user');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return '#6366F1'; // Indigo
      case 'teacher':
        return '#10B981'; // Green
      case 'parent':
        return '#3B82F6'; // Blue
      default:
        return '#6B7280'; // Gray
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return '#10B981'; // Green
      case 'pending':
        return '#F59E0B'; // Yellow
      case 'rejected':
        return '#EF4444'; // Red
      default:
        return '#6B7280'; // Gray
    }
  };

  return (
    <View className="flex-1 bg-white">
      {/* Header */}
      <View className="p-3 border-b border-gray-200 bg-white shadow-sm">
        <View className="flex-row justify-between items-center mb-3">
          <View className="flex-row items-center">
            <TouchableOpacity
              onPress={() => router.back()}
              className="mr-3 p-1"
            >
              <FontAwesome name="arrow-left" size={20} color="#4B5563" />
            </TouchableOpacity>
            <Text className="text-2xl font-bold text-gray-800">User Management</Text>
          </View>
          <TouchableOpacity
            className="bg-blue-500 px-3 py-2 rounded-lg shadow-sm flex-row items-center"
            style={{ elevation: 3 }}
            activeOpacity={0.7}
            onPress={() => setShowAddModal(true)}
          >
            <FontAwesome name="plus-circle" size={16} color="#FFFFFF" style={{ marginRight: 6 }} />
            <Text className="text-white font-medium">Create User</Text>
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View className="flex-row items-center mb-2">
          <View className="flex-row flex-1 items-center bg-gray-100 px-3 py-1.5 rounded-lg">
            <FontAwesome name="search" size={20} color="#6B7280" />
            <TextInput
              className="flex-1 ml-2"
              placeholder="Search users..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <FontAwesome name="times-circle" size={18} color="#6B7280" />
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        {/* Simple Role Filter Tabs */}
        <View className="flex-row mb-3 bg-gray-50 rounded-lg overflow-hidden border border-gray-200 shadow-sm">
          <TouchableOpacity
            className={`flex-1 py-2 ${roleFilter === 'all' ? 'bg-gray-200' : 'bg-gray-50'} border-r border-gray-200 items-center`}
            onPress={() => setRoleFilter('all')}
          >
            <Text className={`font-medium ${roleFilter === 'all' ? 'text-gray-800' : 'text-gray-600'}`}>
              All
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className={`flex-1 py-2 ${roleFilter === 'parent' ? 'bg-blue-100' : 'bg-gray-50'} border-r border-gray-200 items-center`}
            onPress={() => setRoleFilter('parent')}
          >
            <Text className={`font-medium ${roleFilter === 'parent' ? 'text-blue-700' : 'text-gray-600'}`}>
              Parents
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className={`flex-1 py-2 ${roleFilter === 'teacher' ? 'bg-green-100' : 'bg-gray-50'} border-r border-gray-200 items-center`}
            onPress={() => setRoleFilter('teacher')}
          >
            <Text className={`font-medium ${roleFilter === 'teacher' ? 'text-green-700' : 'text-gray-600'}`}>
              Teachers
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className={`flex-1 py-2 ${roleFilter === 'admin' ? 'bg-purple-100' : 'bg-gray-50'} items-center`}
            onPress={() => setRoleFilter('admin')}
          >
            <Text className={`font-medium ${roleFilter === 'admin' ? 'text-purple-700' : 'text-gray-600'}`}>
              Admins
            </Text>
          </TouchableOpacity>
        </View>

        {/* Additional Filters */}
        <View className="flex-row justify-between items-center mb-1.5">
          {/* Sort Control */}
          <View className="flex-row items-center">
            <Text className="text-gray-600 mr-1 text-sm">Sort:</Text>
            <TouchableOpacity
              className="flex-row items-center bg-gray-100 px-2 py-1 rounded-lg"
              onPress={() => {
                setSortBy(sortBy === 'newest' ? 'oldest' : 'newest');
              }}
            >
              <FontAwesome name="sort" size={12} color="#6B7280" style={{ marginRight: 4 }} />
              <Text className="mr-1 text-sm">
                {sortBy === 'newest' ? 'Newest' : 'Oldest'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Reset Button */}
          {(roleFilter !== 'all' || statusFilter !== 'all' || searchQuery) && (
            <TouchableOpacity
              className="bg-gray-100 px-2 py-1 rounded-lg"
              onPress={resetFilters}
            >
              <Text className="text-gray-600 text-sm">Reset Filters</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Mobile-Friendly Results Count Indicator */}
      <View className="bg-white px-4 py-4 border-b border-gray-200 shadow-sm">
        {/* Top row with count and action buttons */}
        <View className="flex-row justify-between items-center">
          <View className="flex-row items-center">
            <View className="w-12 h-12 bg-blue-100 rounded-full items-center justify-center mr-3 border border-blue-200 shadow-sm">
              <Text className="text-blue-600 font-bold text-xl">{filteredUsers.length}</Text>
            </View>
            <View>
              <Text className="text-xs text-gray-500 uppercase font-medium tracking-wider">TOTAL USERS</Text>
              <Text className="text-gray-800 font-bold text-base">
                {filteredUsers.length} {filteredUsers.length === 1 ? 'user' : 'users'} found
              </Text>
            </View>
          </View>

          {/* Action buttons in a row */}
          <View className="flex-row">
            <TouchableOpacity
              className="w-11 h-11 bg-blue-50 rounded-full items-center justify-center mr-2 border border-blue-100 shadow-sm"
              style={{ elevation: 1 }}
              activeOpacity={0.7}
              onPress={fetchUsers}
            >
              <FontAwesome name="refresh" size={18} color="#3B82F6" />
            </TouchableOpacity>

            {(roleFilter !== 'all' || statusFilter !== 'all' || searchQuery) && (
              <TouchableOpacity
                className="w-11 h-11 bg-gray-50 rounded-full items-center justify-center border border-gray-200 shadow-sm"
                style={{ elevation: 1 }}
                activeOpacity={0.7}
                onPress={resetFilters}
              >
                <FontAwesome name="times" size={18} color="#6B7280" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>

      {/* Users List */}
      {loading ? (
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text className="mt-4 text-gray-600">Loading users...</Text>
        </View>
      ) : filteredUsers.length === 0 ? (
        <View className="flex-1 justify-center items-center p-4">
          <FontAwesome name="users" size={50} color="#D1D5DB" />
          <Text className="mt-4 text-gray-500 text-center">
            {searchQuery || roleFilter !== 'all' || statusFilter !== 'all'
              ? 'No users match your current filters'
              : 'No users found'}
          </Text>
          <TouchableOpacity
            className="mt-6 bg-blue-500 px-6 py-3 rounded-lg shadow-sm"
            onPress={() => setShowAddModal(true)}
          >
            <Text className="text-white font-bold text-center">Create New User</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView className="flex-1">

          {filteredUsers.map((user) => (
            <View
              key={user.id}
              className="p-4 border-b border-gray-200"
            >
              <View className="flex-row justify-between items-start">
                <View className="flex-1">
                  <Text className="font-medium text-lg">{user.name}</Text>
                  <Text className="text-gray-600">{user.email}</Text>
                  {user.password && (
                    <View className="flex-row items-center mt-1">
                      <FontAwesome name="key" size={12} color="#6B7280" />
                      <Text className="text-gray-600 ml-1">Password: {user.password}</Text>
                      <TouchableOpacity
                        className="ml-2 bg-gray-100 p-1 rounded-full"
                        onPress={() => {
                          // Copy password to clipboard manually
                          // Since Clipboard API is deprecated, we'll just show the password
                          Alert.alert('Password', `The password for ${user.name} is: ${user.password}`);
                        }}
                      >
                        <FontAwesome name="copy" size={12} color="#6B7280" />
                      </TouchableOpacity>
                    </View>
                  )}
                  {user.phone && (
                    <Text className="text-gray-600 mt-1">
                      <FontAwesome name="phone" size={12} color="#6B7280" /> {user.phone}
                    </Text>
                  )}

                  {/* Role and Status badges */}
                  <View className="flex-row flex-wrap mt-2">
                    <View
                      className="px-2 py-1 rounded-full mr-2 mb-1"
                      style={{ backgroundColor: `${getRoleColor(user.role)}20` }}
                    >
                      <Text
                        className="text-xs capitalize"
                        style={{ color: getRoleColor(user.role) }}
                      >
                        {user.role}
                      </Text>
                    </View>
                    <View
                      className="px-2 py-1 rounded-full mb-1"
                      style={{ backgroundColor: `${getStatusColor(user.status)}20` }}
                    >
                      <Text
                        className="text-xs capitalize"
                        style={{ color: getStatusColor(user.status) }}
                      >
                        {user.status}
                      </Text>
                    </View>
                  </View>

                  {/* Enhanced Teacher Professional Information */}
                  {user.role === 'teacher' && (
                    <View className="mt-2 bg-blue-50 p-3 rounded-lg border border-blue-100 items-center w-full">
                      {/* Professional Details Button */}
                      <TouchableOpacity
                        className="bg-blue-500 p-2.5 rounded-lg flex-row items-center justify-center shadow-sm w-full"
                        style={{ elevation: 2 }}
                        activeOpacity={0.7}
                        onPress={async () => {
                          try {
                            console.log('Fetching teacher details for ID:', user.id);

                            // Query for teacher document by userId
                            const teachersCollection = collection(firestore, 'teachers');
                            const teachersQuery = query(teachersCollection, where('userId', '==', user.id));
                            const querySnapshot = await getDocs(teachersQuery);

                            // Check if we found any matching teacher documents
                            if (!querySnapshot.empty) {
                              const teacherDoc = querySnapshot.docs[0];
                              const data = teacherDoc.data();
                              console.log('Teacher data found:', data);

                              // Store the teacher details in state and show the modal
                              setSelectedTeacherDetails({
                                name: user.name,
                                subject: data.subject || 'Not provided',
                                classTeacher: data.classTeacher || 'Not provided',
                                qualification: data.qualification || 'Not provided',
                                experience: data.experience || 'Not provided',
                                joiningDate: data.joiningDate || 'Not provided'
                              });
                              setShowTeacherDetails(true);
                            } else {
                              console.log('No teacher data found for user ID:', user.id);
                              Alert.alert(
                                'No Professional Details',
                                'No professional details found for this teacher.',
                                [{ text: 'OK', style: 'default' }]
                              );
                            }
                          } catch (error) {
                            console.error('Error fetching teacher details:', error);
                            Alert.alert(
                              'Error',
                              'Failed to fetch teacher details. Please try again.',
                              [{ text: 'OK', style: 'default' }]
                            );
                          }
                        }}
                      >
                        <FontAwesome name="graduation-cap" size={16} color="#FFFFFF" style={{ marginRight: 8 }} />
                        <Text className="text-white font-bold text-center text-sm">Teacher Professional Details</Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
                <TouchableOpacity
                  className="p-2"
                  onPress={() => {
                    setSelectedUser(user);
                    setShowDeleteConfirm(true);
                  }}
                >
                  <FontAwesome name="trash" size={20} color="#EF4444" />
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </ScrollView>
      )}

      {/* Create User Modal */}
      <Modal
        visible={showAddModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAddModal(false)}
      >
        <View className="flex-1 bg-black/50 justify-center items-center">
          <View className="bg-white w-11/12 rounded-xl p-4 shadow-xl">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-xl font-bold text-gray-800">Create New User</Text>
              <TouchableOpacity onPress={() => setShowAddModal(false)}>
                <FontAwesome name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <ScrollView className="max-h-[400px]">
              <View className="space-y-4">
                <View>
                  <Text className="text-gray-700 font-medium mb-1">Name</Text>
                  <TextInput
                    className="border border-gray-300 rounded-lg p-3 bg-gray-50"
                    placeholder="Enter full name"
                    value={newUser.name}
                    onChangeText={(text) => setNewUser({ ...newUser, name: text })}
                  />
                </View>

                <View>
                  <Text className="text-gray-700 font-medium mb-1">Email</Text>
                  <TextInput
                    className="border border-gray-300 rounded-lg p-3 bg-gray-50"
                    placeholder="Enter email address"
                    value={newUser.email}
                    onChangeText={(text) => setNewUser({ ...newUser, email: text })}
                    keyboardType="email-address"
                    autoCapitalize="none"
                  />
                </View>

                <View>
                  <Text className="text-gray-700 font-medium mb-1">Password</Text>
                  <TextInput
                    className="border border-gray-300 rounded-lg p-3 bg-gray-50"
                    placeholder="Enter password"
                    value={newUser.password}
                    onChangeText={(text) => setNewUser({ ...newUser, password: text })}
                    secureTextEntry
                  />
                </View>

                <View>
                  <Text className="text-gray-700 font-medium mb-1">Role</Text>
                  <View className="flex-row flex-wrap">
                    {['parent', 'teacher', 'admin'].map((role) => (
                      <TouchableOpacity
                        key={role}
                        className={`mr-2 mb-2 px-4 py-2 rounded-lg ${
                          newUser.role === role ? 'bg-blue-500' : 'bg-gray-200'
                        }`}
                        onPress={() => setNewUser({ ...newUser, role: role as UserRole })}
                      >
                        <Text
                          className={`capitalize ${
                            newUser.role === role ? 'text-white' : 'text-gray-800'
                          }`}
                        >
                          {role}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                {/* Phone Number - Common for all roles */}
                <View>
                  <Text className="text-gray-700 font-medium mb-1">Phone Number</Text>
                  <TextInput
                    className="border border-gray-300 rounded-lg p-3 bg-gray-50"
                    placeholder="Enter phone number"
                    value={newUser.phone}
                    onChangeText={(text) => setNewUser({ ...newUser, phone: text })}
                    keyboardType="phone-pad"
                  />
                </View>

                {/* Teacher Professional Details */}
                {newUser.role === 'teacher' && (
                  <View className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-100">
                    <View className="flex-row items-center mb-3">
                      <FontAwesome name="graduation-cap" size={18} color="#3B82F6" style={{ marginRight: 8 }} />
                      <Text className="text-blue-700 font-bold text-lg">Professional Information</Text>
                    </View>

                    <View className="space-y-3">
                      {/* Subject */}
                      <View>
                        <Text className="text-gray-700 font-medium mb-1">Subject</Text>
                        <TextInput
                          className="border border-gray-300 rounded-lg p-3 bg-white"
                          placeholder="Enter subject taught"
                          value={newUser.subject}
                          onChangeText={(text) => setNewUser({ ...newUser, subject: text })}
                        />
                      </View>

                      {/* Class Teacher */}
                      <View>
                        <Text className="text-gray-700 font-medium mb-1">Class Teacher</Text>
                        <TextInput
                          className="border border-gray-300 rounded-lg p-3 bg-white"
                          placeholder="Enter class (e.g. 5-A)"
                          value={newUser.classTeacher}
                          onChangeText={(text) => setNewUser({ ...newUser, classTeacher: text })}
                        />
                      </View>

                      {/* Qualification */}
                      <View>
                        <Text className="text-gray-700 font-medium mb-1">Qualification</Text>
                        <TextInput
                          className="border border-gray-300 rounded-lg p-3 bg-white"
                          placeholder="Enter qualification (e.g. M.Sc., B.Ed.)"
                          value={newUser.qualification}
                          onChangeText={(text) => setNewUser({ ...newUser, qualification: text })}
                        />
                      </View>

                      {/* Experience */}
                      <View>
                        <Text className="text-gray-700 font-medium mb-1">Experience</Text>
                        <TextInput
                          className="border border-gray-300 rounded-lg p-3 bg-white"
                          placeholder="Enter experience (e.g. 5 years)"
                          value={newUser.experience}
                          onChangeText={(text) => setNewUser({ ...newUser, experience: text })}
                        />
                      </View>

                      {/* Joining Date */}
                      <View>
                        <Text className="text-gray-700 font-medium mb-1">Joining Date</Text>
                        <TextInput
                          className="border border-gray-300 rounded-lg p-3 bg-white"
                          placeholder="YYYY-MM-DD"
                          value={newUser.joiningDate}
                          onChangeText={(text) => setNewUser({ ...newUser, joiningDate: text })}
                        />
                      </View>
                    </View>
                  </View>
                )}

                <View className="flex-row items-center justify-between mt-4">
                  <Text className="text-gray-700 font-medium">Auto-approve user</Text>
                  <Switch
                    value={newUser.autoApprove}
                    onValueChange={(value) =>
                      setNewUser({ ...newUser, autoApprove: value })
                    }
                    trackColor={{ false: '#D1D5DB', true: '#3B82F6' }}
                    thumbColor="#FFFFFF"
                  />
                </View>
              </View>
            </ScrollView>

            <TouchableOpacity
              className="bg-blue-500 py-3 rounded-lg mt-6"
              onPress={handleCreateUser}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text className="text-white font-medium text-center">Create User</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={showDeleteConfirm}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDeleteConfirm(false)}
      >
        <View className="flex-1 bg-black/50 justify-center items-center">
          <View className="bg-white w-11/12 rounded-xl p-4 shadow-xl">
            <View className="items-center mb-4">
              <View className="w-16 h-16 bg-red-100 rounded-full items-center justify-center mb-4">
                <FontAwesome name="exclamation-triangle" size={32} color="#EF4444" />
              </View>
              <Text className="text-xl font-bold text-gray-800">Confirm Delete</Text>
              <Text className="text-gray-600 text-center mt-2">
                Are you sure you want to delete {selectedUser?.name}? This action cannot be undone.
              </Text>
            </View>

            <View className="flex-row justify-between mt-4">
              <TouchableOpacity
                className="flex-1 bg-gray-200 py-3 rounded-lg mr-2"
                onPress={() => setShowDeleteConfirm(false)}
              >
                <Text className="text-gray-800 font-medium text-center">Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                className="flex-1 bg-red-500 py-3 rounded-lg ml-2"
                onPress={handleDeleteUser}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <ActivityIndicator color="white" />
                ) : (
                  <Text className="text-white font-medium text-center">Delete</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Teacher Professional Details Modal */}
      <Modal
        visible={showTeacherDetails}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowTeacherDetails(false)}
      >
        <View className="flex-1 bg-black/50 justify-center items-center p-4">
          <View className="bg-white w-11/12 rounded-xl overflow-hidden shadow-xl">
            {/* Header */}
            <View className="bg-blue-500 p-4">
              <Text className="text-white text-xl font-bold text-center">
                Professional Information
              </Text>
            </View>

            {selectedTeacherDetails && (
              <View className="p-5">
                {/* Teacher Name */}
                <View className="items-center mb-6">
                  <View className="w-20 h-20 bg-blue-100 rounded-full items-center justify-center mb-3">
                    <FontAwesome name="user" size={40} color="#3B82F6" />
                  </View>
                  <Text className="text-2xl font-bold text-gray-800">{selectedTeacherDetails.name}</Text>
                </View>

                {/* Professional Details */}
                <View className="space-y-4">
                  {/* Subject */}
                  <View className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                    <View className="flex-row items-center mb-1">
                      <FontAwesome name="book" size={16} color="#3B82F6" style={{ marginRight: 8 }} />
                      <Text className="text-gray-500 text-sm font-medium">SUBJECT</Text>
                    </View>
                    <Text className="text-gray-800 text-lg font-medium pl-6">
                      {selectedTeacherDetails.subject}
                    </Text>
                  </View>

                  {/* Class Teacher */}
                  <View className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                    <View className="flex-row items-center mb-1">
                      <FontAwesome name="users" size={16} color="#10B981" style={{ marginRight: 8 }} />
                      <Text className="text-gray-500 text-sm font-medium">CLASS TEACHER</Text>
                    </View>
                    <Text className="text-gray-800 text-lg font-medium pl-6">
                      {selectedTeacherDetails.classTeacher}
                    </Text>
                  </View>

                  {/* Qualification */}
                  <View className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                    <View className="flex-row items-center mb-1">
                      <FontAwesome name="graduation-cap" size={16} color="#8B5CF6" style={{ marginRight: 8 }} />
                      <Text className="text-gray-500 text-sm font-medium">QUALIFICATION</Text>
                    </View>
                    <Text className="text-gray-800 text-lg font-medium pl-6">
                      {selectedTeacherDetails.qualification}
                    </Text>
                  </View>

                  {/* Experience */}
                  <View className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                    <View className="flex-row items-center mb-1">
                      <FontAwesome name="briefcase" size={16} color="#F59E0B" style={{ marginRight: 8 }} />
                      <Text className="text-gray-500 text-sm font-medium">EXPERIENCE</Text>
                    </View>
                    <Text className="text-gray-800 text-lg font-medium pl-6">
                      {selectedTeacherDetails.experience}
                    </Text>
                  </View>

                  {/* Joining Date */}
                  <View className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                    <View className="flex-row items-center mb-1">
                      <FontAwesome name="calendar" size={16} color="#EC4899" style={{ marginRight: 8 }} />
                      <Text className="text-gray-500 text-sm font-medium">JOINING DATE</Text>
                    </View>
                    <Text className="text-gray-800 text-lg font-medium pl-6">
                      {selectedTeacherDetails.joiningDate}
                    </Text>
                  </View>
                </View>

                {/* Close Button */}
                <TouchableOpacity
                  className="mt-6 bg-blue-500 py-3 rounded-lg"
                  onPress={() => setShowTeacherDetails(false)}
                >
                  <Text className="text-white font-bold text-center">Close</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}
