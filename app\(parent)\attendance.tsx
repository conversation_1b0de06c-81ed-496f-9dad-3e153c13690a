import { useState, useEffect, useCallback } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Modal, ActivityIndicator, Alert, RefreshControl } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Calendar } from 'react-native-calendars';
import { useAuth } from '../../hooks/useAuth';
import { FontAwesome } from '@expo/vector-icons';
import { firestore } from '../../config/firebase';
import { collection, getDocs, query, where, doc, getDoc } from 'firebase/firestore';

interface Child {
  id: string;
  name: string;
  class: string;
}

interface AttendanceRecord {
  date: string;
  status: 'present' | 'absent' | 'holiday';
  class?: string;
}

export default function ParentAttendance() {
  const { user } = useAuth();
  const [showChildModal, setShowChildModal] = useState(false);
  const [selectedChild, setSelectedChild] = useState<Child | null>(null);
  const [children, setChildren] = useState<Child[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const [attendanceLoading, setAttendanceLoading] = useState(false);

  // Consolidated loading state
  const isLoading = loading || refreshing || attendanceLoading;

  // Fetch children data from Firestore
  useEffect(() => {
    if (user) {
      fetchChildren();
    }
  }, [user]);

  // Function to handle refresh
  const onRefresh = useCallback(async () => {
    if (loading || attendanceLoading) return; // Prevent multiple refreshes

    setRefreshing(true);
    try {
      if (user) {
        await fetchChildren();
        if (selectedChild) {
          await fetchAttendanceData(selectedChild);
        }
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  }, [user, selectedChild, loading, attendanceLoading]);

  const fetchChildren = async (): Promise<void> => {
    if (!user) return;

    try {
      setLoading(true);

      // First, get the user's phone number from Firestore
      const userRef = doc(firestore, 'users', user.id);
      const userDoc = await getDoc(userRef);
      const userPhone = userDoc.exists() ? userDoc.data().phone : null;

      const childrenCollection = collection(firestore, 'students');

      // Get students where parentPhone matches user's phone number
      // If user doesn't have a phone number, fall back to parentId
      let childrenQuery;
      if (userPhone) {
        console.log('Fetching children by phone number:', userPhone);
        childrenQuery = query(childrenCollection, where('parentPhone', '==', userPhone));
      } else {
        console.log('No phone number found, fetching by parentId');
        childrenQuery = query(childrenCollection, where('parentId', '==', user.id));
      }

      const querySnapshot = await getDocs(childrenQuery);

      const childrenData: Child[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        childrenData.push({
          id: doc.id,
          name: data.name || 'Unknown',
          class: data.class || 'Unknown',
        });
      });

      console.log(`Found ${childrenData.length} children for this parent`);
      setChildren(childrenData);

      // Try to get previously selected child from AsyncStorage
      try {
        const savedChildId = await AsyncStorage.getItem('selectedChildId');
        console.log('Retrieved saved child ID:', savedChildId);

        if (savedChildId) {
          // Find the child with the saved ID
          const savedChild = childrenData.find(child => child.id === savedChildId);
          if (savedChild) {
            console.log('Found saved child:', savedChild.name);
            setSelectedChild(savedChild);
          } else {
            // If saved child not found (maybe removed), select first child
            console.log('Saved child not found in current children list');
            if (childrenData.length > 0) {
              setSelectedChild(childrenData[0]);
            }
          }
        } else if (childrenData.length > 0 && !selectedChild) {
          // If no saved child, select the first one
          setSelectedChild(childrenData[0]);
        }
      } catch (error) {
        console.error('Error retrieving selected child from AsyncStorage:', error);
        // Fallback to selecting first child
        if (childrenData.length > 0 && !selectedChild) {
          setSelectedChild(childrenData[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching children:', error);
      Alert.alert('Error', 'Failed to load children data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // State for attendance records
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);

  // Fetch attendance data when selected child changes
  useEffect(() => {
    if (selectedChild) {
      fetchAttendanceData(selectedChild);
    }
  }, [selectedChild]);

  // Force a refresh when the component mounts
  useEffect(() => {
    // Small delay to ensure the component is fully mounted
    const timer = setTimeout(() => {
      if (selectedChild) {
        console.log('Forcing refresh of attendance data...');
        fetchAttendanceData(selectedChild);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Fetch real attendance data from Firestore
  const fetchAttendanceData = async (child: Child): Promise<void> => {
    if (!child) {
      console.log('No child selected, skipping attendance data fetch');
      return;
    }

    try {
      setAttendanceLoading(true);
      console.log(`Fetching attendance data for student: ${child.name}, class: ${child.class}, id: ${child.id}`);

      // Get all attendance records for the student's class
      const attendanceCollection = collection(firestore, 'attendance');
      console.log('Getting attendance collection...');
      const querySnapshot = await getDocs(attendanceCollection);
      console.log(`Retrieved ${querySnapshot.size} attendance documents`);

      const records: AttendanceRecord[] = [];

      // Process each attendance document
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const docId = doc.id;

        // Check if this document is for the student's class
        // Document ID format is: {className}_{date}
        if (docId.startsWith(`${child.class}_`)) {
          console.log(`Found attendance record for class ${child.class}: ${docId}`);
          // Extract the date from the document ID
          const date = docId.split('_')[1];

          // Check if this student is in the attendance record
          if (data.students && data.students[child.id]) {
            const status = data.students[child.id];
            console.log(`Student ${child.id} status for ${date}: ${status}`);
            // Add the record with the student's status
            records.push({
              date,
              status: status === 'present' ? 'present' : 'absent',
              class: child.class
            });
          } else {
            console.log(`Student ${child.id} not found in attendance record for ${date}`);
          }
        }
      });

      console.log(`Found ${records.length} attendance records for ${child.name}`);

      // Sort records by date (newest first)
      records.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      setAttendanceRecords(records);
    } catch (error) {
      console.error('Error fetching attendance data:', error);
      Alert.alert('Error', 'Failed to load attendance data. Please try again.');
    } finally {
      setAttendanceLoading(false);
    }
  };

  const handleSelectChild = async (child: Child) => {
    setSelectedChild(child);
    setShowChildModal(false);

    // Save selected child to AsyncStorage
    try {
      await AsyncStorage.setItem('selectedChildId', child.id);
      console.log(`Saved selected child ID ${child.id} to AsyncStorage`);
    } catch (error) {
      console.error('Error saving selected child to AsyncStorage:', error);
    }
  };

  // Helper function to format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <View className="flex-1 bg-white">
      {/* Loading Indicator - Only show when not refreshing */}
      {isLoading && !refreshing && (
        <View className="absolute z-10 w-full h-full bg-black/30 items-center justify-center">
          <View className="bg-white p-4 rounded-xl shadow-lg">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="text-gray-700 mt-2 font-medium">Loading...</Text>
          </View>
        </View>
      )}

      {/* Header with Child Selection */}
      <View className="p-4 border-b border-gray-200">
        <TouchableOpacity
          className="flex-row items-center"
          onPress={() => setShowChildModal(true)}
        >
          <Text className="text-lg font-semibold mr-2">
            {selectedChild ? selectedChild.name : 'Select Child'}
          </Text>
          <FontAwesome name="chevron-down" size={16} color="#6b7280" />
        </TouchableOpacity>
        {selectedChild && (
          <Text className="text-gray-600 mt-1">
            Class {selectedChild.class}
          </Text>
        )}
      </View>

      {/* Main Content */}
      {selectedChild && (
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ paddingBottom: 100 }}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#3B82F6']}
              tintColor="#3B82F6"
            />
          }
        >
          {/* Attendance Summary with Dual-Color Indicator */}
          <View className="p-4 mb-4">
            <Text className="text-lg font-semibold mb-3 text-gray-800">Attendance Summary</Text>
            <View className="bg-white p-4 rounded-xl shadow-sm">
              {/* Top row with percentage indicator */}
              <View className="flex-row items-center mb-4">
                {/* Attendance percentage indicator with dual colors */}
                <View className="w-16 h-16 mr-3 bg-gray-100 rounded-lg justify-center items-center overflow-hidden">
                  {/* Red background for absent */}
                  <View className="absolute bottom-0 left-0 right-0 bg-red-500 h-full" />

                  {/* Green overlay for present percentage */}
                  <View
                    className="absolute bottom-0 left-0 right-0 bg-green-500"
                    style={{
                      height: `${attendanceRecords.length > 0 ?
                        (attendanceRecords.filter(record => record.status === 'present').length / attendanceRecords.length * 100).toFixed(0) :
                        '0'}%`
                    }}
                  />

                  {/* Percentage text */}
                  <View className="absolute inset-0 justify-center items-center">
                    <Text
                      className="text-lg font-bold text-white"
                      style={{
                        textShadowColor: 'rgba(0, 0, 0, 0.5)',
                        textShadowOffset: { width: 1, height: 1 },
                        textShadowRadius: 2
                      }}
                    >
                      {attendanceRecords.length > 0 ?
                        (attendanceRecords.filter(record => record.status === 'present').length / attendanceRecords.length * 100).toFixed(0) :
                        '0'}%
                    </Text>
                  </View>
                </View>

                <View className="flex-1">
                  <Text className="text-base font-bold text-gray-800">Attendance Rate</Text>
                  <Text className="text-sm text-gray-600">
                    {attendanceRecords.length > 0 ?
                      `${attendanceRecords.filter(record => record.status === 'present').length} out of ${attendanceRecords.length} days present` :
                      'No attendance records'}
                  </Text>
                </View>
              </View>

              {/* Stats row */}
              <View className="flex-row justify-between">
                <View className="bg-gray-50 rounded-lg p-2 flex-1 mr-2 items-center">
                  <Text className="text-xs text-gray-500 mb-1">Present</Text>
                  <Text className="text-lg font-bold text-green-600">{attendanceRecords.filter(record => record.status === 'present').length}</Text>
                </View>
                <View className="bg-gray-50 rounded-lg p-2 flex-1 mr-2 items-center">
                  <Text className="text-xs text-gray-500 mb-1">Absent</Text>
                  <Text className="text-lg font-bold text-red-600">{attendanceRecords.filter(record => record.status === 'absent').length}</Text>
                </View>
                <View className="bg-gray-50 rounded-lg p-2 flex-1 items-center">
                  <Text className="text-xs text-gray-500 mb-1">Total</Text>
                  <Text className="text-lg font-bold text-gray-800">{attendanceRecords.length}</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Legend */}
          <View className="px-4 py-3 border-t border-gray-200 mb-2">
            <Text className="text-lg font-semibold mb-2">Legend</Text>
            <View className="flex-row space-x-4">
              <View className="flex-row items-center">
                <View className="w-3 h-3 rounded-full bg-green-500 mr-2" />
                <Text>Present</Text>
              </View>
              <View className="flex-row items-center">
                <View className="w-3 h-3 rounded-full bg-red-500 mr-2" />
                <Text>Absent</Text>
              </View>
            </View>
          </View>

          {/* Attendance Records */}
          <View className="px-4 pt-2 pb-6">
            <Text className="text-xl font-bold mb-4 text-gray-800">Attendance Records</Text>
            {attendanceRecords.length === 0 ? (
              <View className="p-6 bg-gray-50 rounded-xl items-center justify-center border border-gray-200">
                <FontAwesome name="calendar-times-o" size={40} color="#9CA3AF" />
                <Text className="text-gray-500 mt-4 text-center">No attendance records found</Text>
              </View>
            ) : (
              <View className="space-y-4">
                {attendanceRecords
                  .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                  .map((record, index) => (
                    <View
                      key={index}
                      className={`p-4 rounded-xl shadow-sm border mb-2 ${record.status === 'present' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}
                    >
                      <View className="flex-row justify-between items-center mb-2">
                        <Text className="font-semibold text-gray-800 flex-shrink mr-2" numberOfLines={1} ellipsizeMode="tail">
                          {formatDate(record.date).split(',')[0]}
                        </Text>
                        <View
                          className={`px-3 py-1 rounded-full ${record.status === 'present' ? 'bg-green-100' : 'bg-red-100'}`}
                        >
                          <Text
                            className={`text-xs font-medium capitalize ${record.status === 'present' ? 'text-green-800' : 'text-red-800'}`}
                          >
                            {record.status}
                          </Text>
                        </View>
                      </View>

                      <View className="flex-row justify-between items-center">
                        <Text className="text-gray-600 text-sm flex-1 mr-2">
                          {formatDate(record.date).split(',').slice(1).join(',').trim()}
                        </Text>
                        <Text className="text-gray-600 text-sm font-medium">Class: {record.class}</Text>
                      </View>
                    </View>
                  ))
                }
              </View>
            )}
          </View>
        </ScrollView>
      )}

      {/* Child Selection Modal */}
      <Modal
        visible={showChildModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowChildModal(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center">
          <View className="bg-white m-4 p-4 rounded-lg">
            <Text className="text-xl font-bold mb-4">Select Child</Text>
            {children.map((child) => (
              <TouchableOpacity
                key={child.id}
                className="p-4 border-b border-gray-200"
                onPress={() => handleSelectChild(child)}
              >
                <Text className="text-lg">{child.name}</Text>
                <Text className="text-gray-600">Class {child.class}</Text>
              </TouchableOpacity>
            ))}
            <TouchableOpacity
              className="mt-4 p-2 bg-gray-200 rounded-lg"
              onPress={() => setShowChildModal(false)}
            >
              <Text className="text-center">Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}