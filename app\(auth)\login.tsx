import { useState, useEffect } from 'react';
import {
  View, Text, TextInput, TouchableOpacity, Vibration,
  Image, KeyboardAvoidingView, Platform, ScrollView, ActivityIndicator,
  useWindowDimensions
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../../hooks/useAuth';
import { FontAwesome } from '@expo/vector-icons';
import CustomToast from '../../components/CustomToast';

export default function LoginScreen() {
  const router = useRouter();
  const { signIn, refreshUserStatus } = useAuth();
  const { width } = useWindowDimensions();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Determine if we're on desktop based on screen width
  const isDesktop = width > 768;

  // Toast state
  const [toast, setToast] = useState({
    visible: false,
    message: '',
    type: 'info' as 'success' | 'error' | 'warning' | 'info'
  });

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleLogin = async () => {
    if (!email || !password) {
      setToast({
        visible: true,
        message: 'Please enter your email and password',
        type: 'warning'
      });
      Vibration.vibrate(200);
      return;
    }

    if (!validateEmail(email)) {
      setToast({
        visible: true,
        message: 'Please enter a valid school email address',
        type: 'warning'
      });
      Vibration.vibrate(200);
      return;
    }

    try {
      setLoading(true);

      // Show toast immediately for better UX
      setToast({
        visible: true,
        message: 'Logging in...',
        type: 'info'
      });

      // Add a small delay to ensure the toast is visible
      await new Promise(resolve => setTimeout(resolve, 300));

      // Perform login
      await signIn(email, password);

      // Refresh user status to ensure we have the latest data
      await refreshUserStatus();

      // Update toast on success
      setToast({
        visible: true,
        message: 'Login successful!',
        type: 'success'
      });

      // Let the _layout.tsx handle the redirection
      // This will avoid showing the index screen

    } catch (error: any) {
      console.log('Login error:', error.code, error.message);

      // Handle specific Firebase error codes with user-friendly messages
      let errorMessage = 'An error occurred';

      if (error.code === 'auth/invalid-credential' || error.code === 'auth/wrong-password') {
        errorMessage = 'Incorrect email or password. Please try again.';
      } else if (error.code === 'auth/user-not-found') {
        errorMessage = 'No account found with this email. Please check your email or register.';
      } else if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Too many failed login attempts. Please try again later.';
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = 'Network error. Please check your internet connection.';
      } else if (error.message) {
        // Use the error message if available
        errorMessage = error.message;
      }

      setToast({
        visible: true,
        message: errorMessage,
        type: 'error'
      });
      Vibration.vibrate([100, 100, 100]);
    } finally {
      setLoading(false);
    }
  };

  // Function to dismiss the toast
  const dismissToast = () => {
    setToast(prev => ({ ...prev, visible: false }));
  };

  // Add useEffect to handle toast auto-dismiss
  useEffect(() => {
    if (toast.visible) {
      const timer = setTimeout(() => {
        dismissToast();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [toast.visible]);

  // No need to redirect here as it's handled by _layout.tsx

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1 bg-gradient-to-b from-blue-50 to-white"
    >
      <ScrollView
        className="flex-1"
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
      >
        <View className="flex-1 px-6 pt-12 pb-8" style={isDesktop ? { maxWidth: 800, width: '100%', alignSelf: 'center' } : {}}>
          {/* Logo & Header */}
          <View className="items-center mb-10">
            <View className="bg-white p-4 rounded-2xl shadow-md mb-6" style={{ borderWidth: 1, borderColor: '#e5e7eb' }}>
              <Image
                source={require('../../assets/logo2.jpeg')}
                style={isDesktop ? { width: 100, height: 100, borderRadius: 16 } : { width: 80, height: 80, borderRadius: 12 }}
                resizeMode="contain"
              />
            </View>
            <Text className={isDesktop ? "text-4xl font-bold text-gray-800" : "text-3xl font-bold text-gray-800"}>School Portal</Text>
            <Text className={isDesktop ? "text-xl text-gray-600 mt-3 text-center" : "text-base text-gray-600 mt-2 text-center"}>Welcome back! Sign in to continue</Text>
          </View>

          {/* Form */}
          <View className="space-y-5 bg-white p-6 rounded-3xl shadow-lg border border-gray-100" style={isDesktop ? { padding: 24, maxWidth: 500, width: '100%', alignSelf: 'center' } : {}}>
            {/* Email */}
            <View>
              <Text className={isDesktop ? "text-base font-medium text-gray-700 mb-2 ml-1" : "text-sm font-medium text-gray-700 mb-1.5 ml-1"}>School Email</Text>
              <View className="flex-row items-center border border-gray-200 rounded-xl bg-gray-50 px-4 shadow-sm">
                <View className={isDesktop ? "bg-blue-100 p-3 rounded-full" : "bg-blue-100 p-2 rounded-full"}>
                  <FontAwesome name="envelope-o" size={isDesktop ? 20 : 18} color="#3B82F6" />
                </View>
                <TextInput
                  className={isDesktop ? "flex-1 h-16 ml-3 text-lg text-gray-900" : "flex-1 h-14 ml-3 text-base text-gray-900"}
                  placeholder="Enter your school email"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
            </View>

            {/* Password */}
            <View>
              <Text className="text-sm font-medium text-gray-700 mb-1.5 ml-1">Password</Text>
              <View className="flex-row items-center border border-gray-200 rounded-xl bg-gray-50 px-4 shadow-sm">
                <View className="bg-blue-100 p-2 rounded-full">
                  <FontAwesome name="lock" size={18} color="#3B82F6" />
                </View>
                <TextInput
                  className="flex-1 h-14 ml-3 text-base text-gray-900"
                  placeholder="Enter your password"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  placeholderTextColor="#9CA3AF"
                />
                <TouchableOpacity
                  onPress={() => setShowPassword(!showPassword)}
                  className="p-2 bg-gray-100 rounded-full"
                >
                  <FontAwesome
                    name={showPassword ? "eye" : "eye-slash"}
                    size={18}
                    color="#6B7280"
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Forgot Password */}
            {/* <TouchableOpacity
              onPress={() => router.push('/(auth)/forgot-password')}
              className="items-end"
            >
              <Text className="text-blue-600 text-sm font-medium">
                Forgot Password?
              </Text>
            </TouchableOpacity> */}

            {/* Submit Button */}
            <TouchableOpacity
              className={`h-14 rounded-xl justify-center items-center mt-6 ${loading ? 'bg-blue-400' : 'bg-blue-600'}`}
              onPress={handleLogin}
              disabled={loading}
              activeOpacity={0.7}
              style={{
                shadowColor: '#3B82F6',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.2,
                shadowRadius: 8,
                elevation: 4
              }}
            >
              {loading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <View className="flex-row items-center">
                  <Text className="text-white font-bold text-base mr-2">
                    Sign In
                  </Text>
                  <FontAwesome name="arrow-right" size={16} color="white" />
                </View>
              )}
            </TouchableOpacity>
          </View>

          {/* Register Link */}
          {/* <View className="flex-row justify-center mt-8 bg-gray-50 py-4 px-6 rounded-xl">
            <Text className="text-gray-600">New to the school portal? </Text>
            <TouchableOpacity>
              <Text className="text-blue-600 font-semibold">Contact School</Text>
            </TouchableOpacity>
          </View> */}

          {/* Help Message */}
          <View className="mt-8">
            <Text className="text-gray-500 text-sm text-center">
              For assistance with your school account,{'\n'}
              please contact the school administration
            </Text>
            <Text className="text-center text-gray-400 text-xs mt-2">Version 1.0.0</Text>
          </View>
        </View>
      </ScrollView>
      <CustomToast
        visible={toast.visible}
        message={toast.message}
        type={toast.type}
        onDismiss={dismissToast}
      />
    </KeyboardAvoidingView>
  );
}
