import AsyncStorage from '@react-native-async-storage/async-storage';
import { Stack, useRouter } from 'expo-router';
import * as Updates from 'expo-updates';
import { useEffect } from 'react';
import { View } from 'react-native';
import { AuthProvider, useAuth } from '../hooks/useAuth';
import './global';

// Route paths are used directly in the navigation logic

// Function to check for updates
const checkForUpdates = async () => {
  try {
    // Skip update check in Expo Go
    if (Updates.channel === undefined || Updates.channel === null) {
      console.log('Skipping update check in Expo Go');
      return;
    }

    console.log('Checking for updates...');
    const update = await Updates.checkForUpdateAsync();

    if (update.isAvailable) {
      console.log('Update available, downloading...');
      const result = await Updates.fetchUpdateAsync();

      if (result.isNew) {
        console.log('Update downloaded, restarting...');
        await Updates.reloadAsync();
      } else {
        console.log('Update downloaded but not new');
      }
    } else {
      console.log('No updates available');
    }
  } catch (error) {
    console.error('Error checking for updates:', error);
  }
};

function RootLayoutNav() {
  const { user, loading, refreshUserStatus } = useAuth();
  const router = useRouter();

  // Check for updates when the app starts
  useEffect(() => {
    checkForUpdates();

    // Only refresh user status on initial mount, not on every user change
    if (user) {
      // Use a flag in localStorage to prevent repeated refreshes
      const refreshOnce = async () => {
        try {
          const lastRefresh = await AsyncStorage.getItem('lastStatusRefresh');
          const now = Date.now();
          // Only refresh if we haven't refreshed in the last minute
          if (!lastRefresh || (now - parseInt(lastRefresh)) > 60000) {
            console.log('Refreshing user status (once per minute max)');
            refreshUserStatus();
            await AsyncStorage.setItem('lastStatusRefresh', now.toString());
          }
        } catch (error) {
          console.error('Error with refresh tracking:', error);
        }
      };
      refreshOnce();
    }
  }, []);

  // This effect runs immediately when the app starts and handles initial routing
  useEffect(() => {
    if (!loading) {
      const navigate = async (path: string) => {
        try {
          // We can't easily check the current path in Expo Router
          // So we'll just use the navigation throttling below

          // Track navigation to prevent loops
          const lastNavTime = await AsyncStorage.getItem('lastNavTime');
          const now = Date.now();

          // Only navigate if we haven't navigated in the last second
          // This prevents rapid navigation loops
          if (!lastNavTime || (now - parseInt(lastNavTime)) > 1000) {
            console.log('Navigating to:', path);
            await AsyncStorage.setItem('lastNavTime', now.toString());
            router.replace(path as any);
          } else {
            console.log('Navigation throttled to prevent loops');
          }
        } catch (error) {
          console.error('Navigation error:', error);
        }
      };

      // Determine where to navigate based on auth state
      if (!user) {
        // Not signed in, redirect to login
        navigate('/(auth)/login');
      } else {
        // Determine target path based on role and navigate immediately
        let targetPath;
        switch (user.role) {
          case 'admin':
            targetPath = '/(admin)/dashboard';
            break;
          case 'parent':
            targetPath = '/(parent)/home';
            break;
          case 'teacher':
            targetPath = '/(teacher)/home';
            break;
          default:
            // Fallback in case role is not set properly
            console.warn('User has no valid role, redirecting to login');
            targetPath = '/(auth)/login';
        }

        // Navigate directly to the target path with high priority
        console.log('Redirecting to:', targetPath);
        navigate(targetPath);
      }
    }
  }, [user, loading]);

  return (
    <View className="flex-1 bg-white">
      {/* Loading state is tracked but no visible overlay */}

      <Stack
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen name="(auth)" />
        <Stack.Screen name="(admin)" />
        <Stack.Screen name="(parent)" />
        <Stack.Screen name="(teacher)" />
      </Stack>
    </View>
  );
}

export default function RootLayout() {
  return (
    <AuthProvider>
      <RootLayoutNav />
    </AuthProvider>
  );
}
