import { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { useAuth } from '../../hooks/useAuth';
import { firestore } from '../../config/firebase';
import { collection, getDocs, query, orderBy, where, doc, updateDoc, arrayUnion, addDoc } from 'firebase/firestore';
import { FontAwesome, MaterialIcons } from '@expo/vector-icons';
import { router } from 'expo-router';

interface Notification {
  id: string;
  title: string;
  message: string;
  date: string;
  targetAudience: 'all' | 'parents' | 'teachers' | 'specific_class';
  specificClass?: string;
  read?: boolean; // Optional as it might be stored in user data
}

interface Event {
  id: string;
  title: string;
  date: string;
  type: 'holiday' | 'exam' | 'activity' | 'other';
  description?: string;
  targetAudience: 'all' | 'parents' | 'teachers' | 'specific_class';
  specificClass?: string;
}

export default function TeacherHome() {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [notificationsLoading, setNotificationsLoading] = useState(true);
  const [upcomingEvents, setUpcomingEvents] = useState<Event[]>([]);
  const [eventsLoading, setEventsLoading] = useState(true);
  const [readNotifications, setReadNotifications] = useState<string[]>([]);

  // Fetch notifications from Firestore
  useEffect(() => {
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    try {
      setNotificationsLoading(true);
      console.log('Fetching notifications from Firestore...');

      // Get announcements collection
      const announcementsCollection = collection(firestore, 'announcements');
      const announcementsQuery = query(announcementsCollection, orderBy('date', 'desc'));
      const querySnapshot = await getDocs(announcementsQuery);

      // Get user's read notifications
      let userReadNotifications: string[] = [];
      if (user?.id) {
        try {
          // Get user's read notifications
          const userSnapshot = await getDocs(query(collection(firestore, 'readNotifications'), where('userId', '==', user.id)));

          if (!userSnapshot.empty) {
            const userData = userSnapshot.docs[0].data();
            userReadNotifications = userData.notificationIds || [];
          }
          setReadNotifications(userReadNotifications);
        } catch (error) {
          console.error('Error fetching read notifications:', error);
        }
      }

      const notificationsData: Notification[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();

        // Only include notifications for all users or specifically for teachers
        if (data.targetAudience === 'all' || data.targetAudience === 'teachers') {
          notificationsData.push({
            id: doc.id,
            title: data.title || 'Untitled',
            message: data.message || '',
            date: data.date || new Date().toISOString().split('T')[0],
            targetAudience: data.targetAudience || 'all',
            specificClass: data.specificClass,
            read: userReadNotifications.includes(doc.id)
          });
        }
      });

      console.log(`Found ${notificationsData.length} notifications for teachers`);
      setNotifications(notificationsData);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      Alert.alert('Error', 'Failed to load notifications. Please try again.');
    } finally {
      setNotificationsLoading(false);
    }
  };

  // Fetch events from Firestore
  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      setEventsLoading(true);
      console.log('Fetching events from Firestore...');

      const eventsCollection = collection(firestore, 'events');
      const eventsQuery = query(eventsCollection, orderBy('date', 'asc'));
      const querySnapshot = await getDocs(eventsQuery);

      const eventsData: Event[] = [];
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison

      console.log('Processing events from Firestore for teacher home...');
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const eventDate = new Date(data.date);

        console.log(`Event: ${data.title}, Date: ${data.date}, Type: ${data.type}, Audience: ${data.targetAudience || 'not specified'}`);

        // Skip past events but include today's events
        const eventDay = new Date(eventDate.getFullYear(), eventDate.getMonth(), eventDate.getDate());
        const todayDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

        console.log(`Event day: ${eventDay.toISOString()}, Today: ${todayDay.toISOString()}, Is past? ${eventDay < todayDay}`);

        // Skip if event date is before today
        if (eventDay < todayDay) {
          console.log(`Skipping past event: ${data.title}`);
          return;
        }

        // Include events that are for all or specifically for teachers
        if (data.targetAudience === 'all' || data.targetAudience === 'teachers' || !data.targetAudience) {
          console.log(`Adding event to list: ${data.title}`);
          eventsData.push({
            id: doc.id,
            title: data.title || 'Untitled Event',
            date: data.date || new Date().toISOString().split('T')[0],
            type: data.type || 'activity',
            description: data.description || '',
            targetAudience: data.targetAudience || 'all',
            specificClass: data.specificClass,
          });
        } else {
          console.log(`Skipping event not for teachers: ${data.title}`);
        }
      });

      // Sort events by date (closest first)
      eventsData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      console.log(`Found ${eventsData.length} events for teachers`);
      setUpcomingEvents(eventsData);
    } catch (error) {
      console.error('Error fetching events:', error);
      Alert.alert('Error', 'Failed to load events. Please try again.');
    } finally {
      setEventsLoading(false);
    }
  };

  const markNotificationAsRead = async (id: string) => {
    try {
      // Update local state
      setNotifications(
        notifications.map((notification) =>
          notification.id === id ? { ...notification, read: true } : notification
        )
      );

      // Add to read notifications array
      setReadNotifications([...readNotifications, id]);

      // Update in Firestore if user is logged in
      if (user?.id) {
        const userReadNotificationsQuery = query(collection(firestore, 'readNotifications'), where('userId', '==', user.id));
        const querySnapshot = await getDocs(userReadNotificationsQuery);

        if (querySnapshot.empty) {
          // Create new document for this user
          await addDoc(collection(firestore, 'readNotifications'), {
            userId: user.id,
            notificationIds: [id]
          });
        } else {
          // Update existing document
          const docRef = doc(firestore, 'readNotifications', querySnapshot.docs[0].id);
          await updateDoc(docRef, {
            notificationIds: arrayUnion(id)
          });
        }
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'activity':
        return 'bg-blue-100 text-blue-800';
      case 'exam':
        return 'bg-red-100 text-red-800';
      case 'holiday':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  return (
    <ScrollView className="flex-1 bg-gray-50" contentContainerStyle={{ paddingBottom: 100 }}>
      {/* Header with Logo - Matching Admin/Parent Style */}
      <View
        className="bg-blue-500 p-6 pt-12 pb-8 rounded-b-3xl mb-6"
        style={{
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.2,
          shadowRadius: 8,
          elevation: 10
        }}
      >
        <View className="flex-row justify-between items-center">
          <View className="flex-row items-center">
            <View className="w-12 h-12 bg-white rounded-full items-center justify-center mr-3 shadow-md" style={{ elevation: 5 }}>
              <FontAwesome name="graduation-cap" size={24} color="#3B82F6" />
            </View>
            <View>
              <Text className="text-white text-3xl font-bold">NUTKHUT</Text>
              <View className="h-1 w-20 bg-white/40 rounded-full mt-1"></View>
            </View>
          </View>
          <TouchableOpacity
            className="w-12 h-12 bg-white rounded-full items-center justify-center shadow-lg"
            onPress={() => router.push('/(teacher)/profile')}
            style={{
              elevation: 5,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.2,
              shadowRadius: 4,
            }}
          >
            <FontAwesome name="user" size={22} color="#3B82F6" />
          </TouchableOpacity>
        </View>

        <View className="mt-4 mb-2 pl-1">
          <Text className="text-white/80 text-base font-medium">Welcome,</Text>
          <Text className="text-white text-2xl font-bold">
            {user?.name || 'Teacher'}
          </Text>
        </View>
      </View>

      <View className="px-4">
        {/* Quick Actions */}
        <View className="mb-6">
          <Text className="text-xl font-bold mb-4 text-gray-800">Quick Actions</Text>
          <View className="flex-row flex-wrap justify-between">
            <TouchableOpacity
              className="bg-white rounded-xl p-5 shadow-md items-center justify-center w-[48%] mb-4"
              style={{
                elevation: 3,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 3,
              }}
              onPress={() => router.push('/(teacher)/attendance')}
            >
              <View className="bg-blue-100 p-4 rounded-full mb-3">
                <FontAwesome name="calendar-check-o" size={26} color="#3B82F6" />
              </View>
              <Text className="font-bold text-gray-800 text-base">Take Attendance</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="bg-white rounded-xl p-5 shadow-md items-center justify-center w-[48%] mb-4"
              style={{
                elevation: 3,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 3,
              }}
              onPress={() => router.push('/(teacher)/calendar')}
            >
              <View className="bg-green-100 p-4 rounded-full mb-3">
                <FontAwesome name="calendar" size={26} color="#10B981" />
              </View>
              <Text className="font-bold text-gray-800 text-base">View Calendar</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="bg-white rounded-xl p-5 shadow-md items-center justify-center w-[48%] mb-4"
              style={{
                elevation: 3,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 3,
              }}
              onPress={() => router.push('/(teacher)/students')}
            >
              <View className="bg-purple-100 p-4 rounded-full mb-3">
                <FontAwesome name="users" size={26} color="#8B5CF6" />
              </View>
              <Text className="font-bold text-gray-800 text-base">Student Directory</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="bg-white rounded-xl p-5 shadow-md items-center justify-center w-[48%] mb-4"
              style={{
                elevation: 3,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 3,
              }}
              onPress={() => router.push('/(teacher)/profile')}
            >
              <View className="bg-orange-100 p-4 rounded-full mb-3">
                <FontAwesome name="user" size={26} color="#F59E0B" />
              </View>
              <Text className="font-bold text-gray-800 text-base">My Profile</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Notifications Section */}
        <View className="mb-6">
          <View className="flex-row justify-between items-center mb-4">
            <Text className="text-lg font-semibold text-gray-800">Notifications</Text>
            <TouchableOpacity
              className="bg-blue-100 px-3 py-1 rounded-full"
              onPress={fetchNotifications}
            >
              <Text className="text-blue-600 text-xs">Refresh</Text>
            </TouchableOpacity>
          </View>

          {notificationsLoading ? (
            <View className="items-center justify-center py-8">
              <ActivityIndicator size="large" color="#3B82F6" />
              <Text className="text-gray-500 mt-2">Loading notifications...</Text>
            </View>
          ) : notifications.length === 0 ? (
            <View className="items-center justify-center py-8 bg-white rounded-xl p-4">
              <MaterialIcons name="notifications-none" size={40} color="#D1D5DB" />
              <Text className="text-gray-500 mt-2">No notifications</Text>
            </View>
          ) : (
            notifications.slice(0, 3).map((notification) => (
              <TouchableOpacity
                key={notification.id}
                className={`mb-3 p-4 rounded-xl shadow-sm border border-gray-100 ${
                  notification.read ? 'bg-white' : 'bg-blue-50'
                }`}
                onPress={() => markNotificationAsRead(notification.id)}
              >
                <View className="flex-row justify-between items-center mb-2">
                  <Text className="font-medium text-gray-800">{notification.title}</Text>
                  {!notification.read && (
                    <View className="bg-blue-500 rounded-full w-2 h-2" />
                  )}
                </View>
                <Text className="text-gray-600 mb-2" numberOfLines={2}>{notification.message}</Text>
                <Text className="text-gray-500 text-xs">{formatDate(notification.date)}</Text>
              </TouchableOpacity>
            ))
          )}

          {notifications.length > 3 && (
            <TouchableOpacity
              className="items-center py-2"
              onPress={() => Alert.alert('Info', 'View all notifications feature coming soon!')}
            >
              <Text className="text-blue-600">View all notifications</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Upcoming Events Section */}
        <View className="mb-6">
          <View className="flex-row justify-between items-center mb-4">
            <Text className="text-lg font-semibold text-gray-800">Upcoming Events</Text>
            <TouchableOpacity
              className="bg-blue-100 px-3 py-1 rounded-full"
              onPress={fetchEvents}
            >
              <Text className="text-blue-600 text-xs">Refresh</Text>
            </TouchableOpacity>
          </View>

          {eventsLoading ? (
            <View className="items-center justify-center py-8">
              <ActivityIndicator size="large" color="#3B82F6" />
              <Text className="text-gray-500 mt-2">Loading events...</Text>
            </View>
          ) : upcomingEvents.length === 0 ? (
            <View className="items-center justify-center py-8 bg-white rounded-xl p-4">
              <FontAwesome name="calendar-o" size={40} color="#D1D5DB" />
              <Text className="text-gray-500 mt-2">No upcoming events</Text>
            </View>
          ) : (
            upcomingEvents.slice(0, 3).map((event) => (
              <View
                key={event.id}
                className="mb-3 p-4 bg-white rounded-xl shadow-sm border border-gray-100"
                style={{
                  borderLeftWidth: 4,
                  borderLeftColor: event.type === 'holiday' ? '#FCD34D' :
                                  event.type === 'exam' ? '#F87171' :
                                  event.type === 'activity' ? '#60A5FA' : '#A3A3A3'
                }}
              >
                <View className="flex-row justify-between items-center mb-2">
                  <Text className="font-medium text-gray-800">{event.title}</Text>
                  <View
                    className={`px-2 py-1 rounded-full ${getEventTypeColor(
                      event.type
                    )}`}
                  >
                    <Text className="text-xs capitalize">{event.type}</Text>
                  </View>
                </View>
                <Text className="text-gray-600 mb-2" numberOfLines={2}>{event.description}</Text>
                <Text className="text-gray-500 text-xs">{formatDate(event.date)}</Text>
              </View>
            ))
          )}

          {upcomingEvents.length > 3 && (
            <TouchableOpacity
              className="items-center py-2"
              onPress={() => router.push('/(teacher)/calendar')}
            >
              <Text className="text-blue-600">View all events</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </ScrollView>
  );
}