import { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { useAuth } from '../../../hooks/useAuth';
import { FontAwesome } from '@expo/vector-icons';
import { firestore } from '../../../config/firebase';
import { collection, addDoc, getDocs, deleteDoc, doc, query, orderBy } from 'firebase/firestore';

interface Notification {
  id: string;
  title: string;
  message: string;
  targetAudience: 'all' | 'parents' | 'teachers' | 'specific_class';
  specificClass?: string;
  date: string;
}

export default function AdminNotifications() {
  // We don't need user info for this screen
  useAuth(); // Keep the auth context active
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);

  const [newNotification, setNewNotification] = useState<Partial<Notification>>({
    title: '',
    message: '',
    targetAudience: 'all',
  });

  // Fetch notifications from Firestore when component mounts
  useEffect(() => {
    fetchNotifications();
  }, []);

  // Helper function to get color based on notification target audience
  const getNotificationColor = (targetAudience: string): string => {
    switch (targetAudience) {
      case 'all':
        return '#3B82F6'; // Blue
      case 'parents':
        return '#10B981'; // Green
      case 'teachers':
        return '#F59E0B'; // Amber
      case 'specific_class':
        return '#8B5CF6'; // Purple
      default:
        return '#6B7280'; // Gray
    }
  };

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      console.log('Fetching notifications from Firestore...');

      const notificationsCollection = collection(firestore, 'announcements');
      const notificationsQuery = query(notificationsCollection, orderBy('date', 'desc'));
      const querySnapshot = await getDocs(notificationsQuery);

      const notificationsData: Notification[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        notificationsData.push({
          id: doc.id,
          title: data.title || 'Untitled',
          message: data.message || '',
          targetAudience: data.targetAudience || 'all',
          specificClass: data.specificClass,
          date: data.date || new Date().toISOString().split('T')[0],
        });
      });

      console.log(`Fetched ${notificationsData.length} notifications`);
      setNotifications(notificationsData);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      Alert.alert('Error', 'Failed to load notifications. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSendNotification = async () => {
    if (!newNotification.title || !newNotification.message) {
      Alert.alert('Error', 'Please fill all required fields');
      return;
    }

    try {
      setLoading(true);
      console.log('Sending notification to Firestore...');

      // Create notification object without ID (Firestore will generate one)
      const notificationData: any = {
        title: newNotification.title,
        message: newNotification.message,
        targetAudience: newNotification.targetAudience || 'all',
        date: new Date().toISOString().split('T')[0],
        createdAt: new Date().toISOString(),
        createdBy: 'admin',
      };

      // Add specificClass if it exists
      if (newNotification.specificClass) {
        notificationData.specificClass = newNotification.specificClass;
      }

      // Add to Firestore
      const announcementsCollection = collection(firestore, 'announcements');
      await addDoc(announcementsCollection, notificationData);

      // Reset form
      setNewNotification({
        title: '',
        message: '',
        targetAudience: 'all',
      });

      setShowAddModal(false);
      Alert.alert('Success', 'Notification sent successfully');

      // Refresh notifications list
      fetchNotifications();
    } catch (error) {
      console.error('Error sending notification:', error);
      Alert.alert('Error', 'Failed to send notification. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteNotification = (id: string) => {
    Alert.alert(
      'Confirm Delete',
      'Are you sure you want to delete this notification?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              console.log('Deleting notification from Firestore...');

              // Delete from Firestore
              const notificationRef = doc(firestore, 'announcements', id);
              await deleteDoc(notificationRef);

              console.log('Notification deleted successfully');
              Alert.alert('Success', 'Notification deleted successfully');

              // Refresh notifications list
              fetchNotifications();
            } catch (error) {
              console.error('Error deleting notification:', error);
              Alert.alert('Error', 'Failed to delete notification. Please try again.');
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  return (
    <View className="flex-1 bg-white">
      {/* Header - Mobile-friendly */}
      <View className="pt-4 pb-4 px-4 border-b border-gray-200 bg-white"
        style={{
          elevation: 2,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
        }}>
        {/* Title with icon */}
        <View className="mb-3">
          <View className="flex-row items-center mb-1">
            <View className="bg-blue-100 p-2 rounded-full mr-3">
              <FontAwesome name="bell" size={20} color="#3B82F6" />
            </View>
            <Text className="text-xl font-bold text-gray-800">Notifications</Text>
          </View>
          <Text className="text-gray-600 text-sm pl-10">Send announcements to the school community</Text>
        </View>

        {/* Add Button - Mobile-friendly */}
        <TouchableOpacity
          className="bg-blue-500 py-4 px-5 rounded-xl flex-row items-center justify-center mt-3"
          style={{
            backgroundColor: '#2563EB', // Darker blue for better visibility
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 0.3,
            shadowRadius: 4,
            elevation: 4,
            borderWidth: 0, // Remove border for cleaner look on mobile
          }}
          onPress={() => setShowAddModal(true)}
          disabled={loading}
          activeOpacity={0.7}
        >
          {loading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <>
              <View className="bg-white/30 rounded-full p-2.5 mr-3">
                <FontAwesome name="plus" size={18} color="white" />
              </View>
              <Text className="text-white font-bold text-lg">New Notification</Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      {/* Notifications List */}
      <ScrollView className="flex-1">
        {loading && notifications.length === 0 ? (
          <View className="p-8 items-center justify-center">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="text-gray-500 mt-4">Loading notifications...</Text>
          </View>
        ) : notifications.length === 0 ? (
          <View className="p-8 items-center justify-center">
            <FontAwesome name="bell-slash" size={40} color="#D1D5DB" />
            <Text className="text-gray-500 mt-4 text-center">No notifications yet</Text>
            <Text className="text-gray-400 text-sm text-center mt-2">
              Create a new notification to send announcements to the school community
            </Text>
          </View>
        ) : (
          notifications.map((notification) => (
          <TouchableOpacity
            key={notification.id}
            className="mx-4 my-2 bg-white rounded-xl shadow-sm overflow-hidden"
            style={{
              borderLeftWidth: 4,
              borderLeftColor: getNotificationColor(notification.targetAudience),
              elevation: 2,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: 0.1,
              shadowRadius: 2,
            }}
            onPress={() => setSelectedNotification(notification)}
            activeOpacity={0.7}
          >
            <View className="p-4">
              <View className="flex-row justify-between items-start">
                <View className="flex-1 pr-4">
                  <Text className="font-bold text-lg text-gray-800">{notification.title}</Text>
                  <Text className="text-gray-600 mt-1 mb-2" numberOfLines={2}>
                    {notification.message}
                  </Text>
                  <View className="flex-row items-center mt-1 flex-wrap">
                    <View className="flex-row items-center bg-gray-100 rounded-full px-3 py-1 mr-2 mb-1">
                      <FontAwesome name="calendar" size={12} color="#6B7280" style={{ marginRight: 4 }} />
                      <Text className="text-gray-600 text-xs">
                        {new Date(notification.date).toLocaleDateString('en-US', { day: 'numeric', month: 'short', year: 'numeric' })}
                      </Text>
                    </View>
                    <View className="flex-row items-center bg-gray-100 rounded-full px-3 py-1 mb-1">
                      <FontAwesome name="users" size={12} color="#6B7280" style={{ marginRight: 4 }} />
                      <Text className="text-gray-600 text-xs capitalize">
                        {notification.targetAudience === 'specific_class' ? 'Class' : notification.targetAudience}
                        {notification.specificClass && `: ${notification.specificClass}`}
                      </Text>
                    </View>
                  </View>
                </View>
                <TouchableOpacity
                  className="bg-red-50 p-2 rounded-full"
                  onPress={() => handleDeleteNotification(notification.id)}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <FontAwesome name="trash" size={16} color="#EF4444" />
                </TouchableOpacity>
              </View>
            </View>
          </TouchableOpacity>
        )))
        }
      </ScrollView>

      {/* Send Notification Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAddModal(false)}
      >
        <View className="flex-1 bg-black/50 justify-center">
          <View className="bg-white mx-4 rounded-2xl overflow-hidden"
            style={{
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              elevation: 5
            }}>
            {/* Modal Header */}
            <View className="bg-blue-500 p-4">
              <View className="flex-row justify-between items-center">
                <Text className="text-xl font-bold text-white">Create Notification</Text>
                <TouchableOpacity
                  onPress={() => setShowAddModal(false)}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <FontAwesome name="times" size={20} color="white" />
                </TouchableOpacity>
              </View>
            </View>

            <ScrollView className="max-h-[500px]">
              <View className="p-5 space-y-4">
                {/* Title Input */}
                <View>
                  <Text className="text-gray-700 font-medium mb-1">Title</Text>
                  <TextInput
                    className="p-3 border border-gray-300 rounded-lg bg-gray-50"
                    placeholder="Enter notification title"
                    placeholderTextColor="#9CA3AF"
                    value={newNotification.title}
                    onChangeText={(text) =>
                      setNewNotification({ ...newNotification, title: text })
                    }
                  />
                </View>

                {/* Message Input */}
                <View>
                  <Text className="text-gray-700 font-medium mb-1">Message</Text>
                  <TextInput
                    className="p-3 border border-gray-300 rounded-lg bg-gray-50"
                    placeholder="Enter notification message"
                    placeholderTextColor="#9CA3AF"
                    value={newNotification.message}
                    onChangeText={(text) =>
                      setNewNotification({ ...newNotification, message: text })
                    }
                    multiline
                    numberOfLines={4}
                    textAlignVertical="top"
                    style={{ minHeight: 100 }}
                  />
                </View>
                {/* Target Audience */}
                <View>
                  <Text className="text-gray-700 font-medium mb-2">Target Audience</Text>
                  <View className="flex-row flex-wrap">
                    {/* All */}
                    <TouchableOpacity
                      className={`mr-2 mb-2 px-4 py-2 rounded-full border ${newNotification.targetAudience === 'all'
                        ? 'bg-blue-500 border-blue-500'
                        : 'bg-white border-gray-300'}`}
                      onPress={() => setNewNotification({ ...newNotification, targetAudience: 'all' })}
                    >
                      <View className="flex-row items-center">
                        <FontAwesome
                          name="users"
                          size={14}
                          color={newNotification.targetAudience === 'all' ? 'white' : '#6B7280'}
                          style={{ marginRight: 6 }}
                        />
                        <Text className={newNotification.targetAudience === 'all' ? 'text-white' : 'text-gray-700'}>
                          Everyone
                        </Text>
                      </View>
                    </TouchableOpacity>
                    {/* Parents */}
                    <TouchableOpacity
                      className={`mr-2 mb-2 px-4 py-2 rounded-full border ${newNotification.targetAudience === 'parents'
                        ? 'bg-green-500 border-green-500'
                        : 'bg-white border-gray-300'}`}
                      onPress={() => setNewNotification({ ...newNotification, targetAudience: 'parents' })}
                    >
                      <View className="flex-row items-center">
                        <FontAwesome
                          name="user"
                          size={14}
                          color={newNotification.targetAudience === 'parents' ? 'white' : '#6B7280'}
                          style={{ marginRight: 6 }}
                        />
                        <Text className={newNotification.targetAudience === 'parents' ? 'text-white' : 'text-gray-700'}>
                          Parents
                        </Text>
                      </View>
                    </TouchableOpacity>
                    {/* Teachers */}
                    <TouchableOpacity
                      className={`mr-2 mb-2 px-4 py-2 rounded-full border ${newNotification.targetAudience === 'teachers'
                        ? 'bg-amber-500 border-amber-500'
                        : 'bg-white border-gray-300'}`}
                      onPress={() => setNewNotification({ ...newNotification, targetAudience: 'teachers' })}
                    >
                      <View className="flex-row items-center">
                        <FontAwesome
                          name="graduation-cap"
                          size={14}
                          color={newNotification.targetAudience === 'teachers' ? 'white' : '#6B7280'}
                          style={{ marginRight: 6 }}
                        />
                        <Text className={newNotification.targetAudience === 'teachers' ? 'text-white' : 'text-gray-700'}>
                          Teachers
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
                {/* Specific Class Button */}
                <View>
                  <TouchableOpacity
                    className={`mb-2 px-4 py-2 rounded-full border ${newNotification.targetAudience === 'specific_class'
                      ? 'bg-purple-500 border-purple-500'
                      : 'bg-white border-gray-300'}`}
                    onPress={() => setNewNotification({ ...newNotification, targetAudience: 'specific_class' })}
                  >
                    <View className="flex-row items-center">
                      <FontAwesome
                        name="book"
                        size={14}
                        color={newNotification.targetAudience === 'specific_class' ? 'white' : '#6B7280'}
                        style={{ marginRight: 6 }}
                      />
                      <Text className={newNotification.targetAudience === 'specific_class' ? 'text-white' : 'text-gray-700'}>
                        Specific Class
                      </Text>
                    </View>
                  </TouchableOpacity>

                  {newNotification.targetAudience === 'specific_class' && (
                    <TextInput
                      className="mt-2 p-3 border border-gray-300 rounded-lg bg-gray-50"
                      placeholder="Enter class (e.g. Class 5-A)"
                      placeholderTextColor="#9CA3AF"
                      value={newNotification.specificClass}
                      onChangeText={(text) =>
                        setNewNotification({ ...newNotification, specificClass: text })
                      }
                    />
                  )}
                </View>
              </View>
            </ScrollView>
            {/* Action Buttons */}
            <View className="p-4 border-t border-gray-200 bg-gray-50">
              <View className="flex-row space-x-3">
                <TouchableOpacity
                  className="flex-1 p-3 bg-gray-200 rounded-lg"
                  onPress={() => setShowAddModal(false)}
                >
                  <Text className="text-gray-700 text-center font-medium">Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  className="flex-1 p-3 bg-blue-500 rounded-lg flex-row justify-center items-center"
                  onPress={handleSendNotification}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator size="small" color="white" />
                  ) : (
                    <>
                      <FontAwesome name="paper-plane" size={14} color="white" style={{ marginRight: 6 }} />
                      <Text className="text-white text-center font-medium">Send Notification</Text>
                    </>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      {/* Notification Details Modal */}
      <Modal
        visible={!!selectedNotification}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setSelectedNotification(null)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-center">
          <View className="bg-white m-4 p-4 rounded-lg">
            {selectedNotification && (
              <>
                <Text className="text-xl font-bold mb-4">Notification Details</Text>
                <ScrollView>
                  <View className="space-y-2">
                    <View>
                      <Text className="text-gray-600">Title</Text>
                      <Text className="text-lg">{selectedNotification.title}</Text>
                    </View>
                    <View>
                      <Text className="text-gray-600">Message</Text>
                      <Text className="text-lg">{selectedNotification.message}</Text>
                    </View>
                    <View>
                      <Text className="text-gray-600">Date</Text>
                      <Text className="text-lg">
                        {new Date(selectedNotification.date).toLocaleDateString()}
                      </Text>
                    </View>
                    <View>
                      <Text className="text-gray-600">Target Audience</Text>
                      <Text className="text-lg capitalize">
                        {selectedNotification.targetAudience}
                        {selectedNotification.specificClass &&
                          ` (${selectedNotification.specificClass})`}
                      </Text>
                    </View>
                  </View>
                </ScrollView>
                <TouchableOpacity
                  className="mt-4 p-2 bg-gray-200 rounded-lg"
                  onPress={() => setSelectedNotification(null)}
                >
                  <Text className="text-center">Close</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}