import { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert } from 'react-native';
import { Link } from 'expo-router';
import { useAuth } from '../../hooks/useAuth';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const { resetPassword } = useAuth();

  const handleResetPassword = async () => {
    try {
      await resetPassword(email);
      Alert.alert(
        'Success',
        'Password reset email sent. Please check your inbox.'
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to send reset email. Please try again.');
    }
  };

  return (
    <View className="flex-1 items-center justify-center p-4 bg-white">
      <Text className="text-2xl font-bold mb-8">Reset Password</Text>
      
      <View className="w-full max-w-sm">
        <Text className="text-gray-600 mb-4">
          Enter your email address and we'll send you a link to reset your password.
        </Text>
        
        <TextInput
          className="w-full p-4 border border-gray-300 rounded-lg mb-4"
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
        />
        
        <TouchableOpacity
          className="w-full bg-blue-500 p-4 rounded-lg mb-4"
          onPress={handleResetPassword}
        >
          <Text className="text-white text-center font-bold">
            Send Reset Link
          </Text>
        </TouchableOpacity>
        
        <Link href="/login" asChild>
          <TouchableOpacity>
            <Text className="text-blue-500 text-center">Back to Login</Text>
          </TouchableOpacity>
        </Link>
      </View>
    </View>
  );
} 