import '../../global.css';
import { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useAuth } from '../../../hooks/useAuth';
import { router } from 'expo-router';
import { signOut as firebaseSignOut } from 'firebase/auth';
import { auth } from '../../../config/firebase';

interface NotificationSetting {
  type: string;
  enabled: boolean;
  description: string;
}

export default function AdminSettings() {
  const { user } = useAuth();
  // Removed unused variables
  const [notificationSettings, setNotificationSettings] = useState<NotificationSetting[]>([
    {
      type: 'attendance',
      enabled: true,
      description: 'Get notified when attendance is marked',
    },
    {
      type: 'payments',
      enabled: true,
      description: 'Receive payment status updates',
    },
    {
      type: 'events',
      enabled: true,
      description: 'Stay updated about school events',
    },
  ]);
  const [isLoading, setIsLoading] = useState(false);

  const handleSendTeacherNotification = () => {
    Alert.prompt(
      'Send Teacher Notification',
      'Enter notification message:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send',
          onPress: (message) => {
            if (message) {
              Alert.alert('Mock Notification Sent', `To Teachers: ${message}`);
            }
          },
        },
      ],
      'plain-text'
    );
  };

  const handleSendParentNotification = () => {
    Alert.prompt(
      'Send Parent Notification',
      'Enter notification message:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send',
          onPress: (message) => {
            if (message) {
              Alert.alert('Mock Notification Sent', `To Parents: ${message}`);
            }
          },
        },
      ],
      'plain-text'
    );
  };

  const handleNotificationToggle = (type: string) => {
    setNotificationSettings((settings) =>
      settings.map((setting) =>
        setting.type === type
          ? { ...setting, enabled: !setting.enabled }
          : setting
      )
    );
  };

  // Removed unused handleSignOut function

  // Direct sign out without confirmation
  const handleDirectSignOut = async () => {
    console.log('Direct sign out initiated');
    try {
      setIsLoading(true);
      // Use Firebase's signOut function directly
      await firebaseSignOut(auth);
      console.log('Firebase signOut completed successfully');
      // Force navigation to login page
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Sign out error:', error);
      Alert.alert('Error', 'Failed to sign out. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = () => {
    Alert.prompt(
      'Reset Password',
      'Enter your email to receive password reset instructions:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send Email',
          onPress: async (email) => {
            if (!email) {
              Alert.alert('Error', 'Please enter your email address');
              return;
            }

            try {
              setIsLoading(true);
              // Use the resetPassword function from useAuth
              await useAuth().resetPassword(email);
              Alert.alert('Success', 'Password reset email sent successfully');
            } catch (error) {
              console.error('Password reset error:', error);
              Alert.alert('Error', 'Failed to send reset email. Please try again.');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ],
      'plain-text',
      user?.email || ''
    );
  };

  return (
    <View className="flex-1 bg-gray-50">
      {isLoading && (
        <View className="absolute z-10 w-full h-full items-center justify-center bg-black/30">
          <View className="bg-white p-4 rounded-xl shadow-lg">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="text-gray-700 mt-2">Please wait...</Text>
          </View>
        </View>
      )}
      {/* Header */}
      {/* <View className="bg-white px-4 py-6 border-b border-gray-200">
        <Text className="text-2xl font-bold text-gray-800">Settings</Text>
        <Text className="text-sm text-gray-500 mt-1">
          Manage your account and preferences
        </Text>
      </View> */}

      <ScrollView className="flex-1">
        {/* Profile Section */}
        {/* <View className="bg-white mt-4">
          <View className="p-4 border-b border-gray-200">
            <Text className="text-lg font-semibold text-gray-800">Profile Settings</Text>
          </View>

          <TouchableOpacity
            className="flex-row items-center justify-between p-4 border-b border-gray-100"
            onPress={() => {
              Alert.alert('Info', 'Edit profile functionality to be implemented');
            }}
          >
            <View className="flex-row items-center">
              <View className="w-10 h-10 rounded-full bg-blue-100 items-center justify-center">
                <FontAwesome name="user" size={20} color="#3B82F6" />
              </View>
              <View className="ml-4">
                <Text className="text-base font-medium text-gray-800">Edit Profile</Text>
                <Text className="text-sm text-gray-500">Update your personal information</Text>
              </View>
            </View>
            <FontAwesome name="chevron-right" size={16} color="#6B7280" />
          </TouchableOpacity>

          <TouchableOpacity
            className="flex-row items-center justify-between p-5"
            onPress={handleResetPassword}
            style={{
              borderBottomWidth: 1,
              borderBottomColor: '#F3F4F6',
            }}
            activeOpacity={0.6}
          >
            <View className="flex-row items-center">
              <View className="w-12 h-12 rounded-full bg-blue-100 items-center justify-center shadow-sm"
                style={{
                  elevation: 1,
                  shadowColor: '#3B82F6',
                  shadowOffset: { width: 0, height: 1 },
                  shadowOpacity: 0.2,
                  shadowRadius: 2,
                }}
              >
                <FontAwesome name="lock" size={22} color="#3B82F6" />
              </View>
              <View className="ml-4">
                <Text className="text-base font-semibold text-gray-800">Change Password</Text>
                <Text className="text-sm text-gray-500">Update your password</Text>
              </View>
            </View>
            <View className="bg-gray-100 rounded-full p-2">
              <FontAwesome name="chevron-right" size={16} color="#3B82F6" />
            </View>
          </TouchableOpacity>
        </View> */}

        {/* Notification Settings */}
        <View className="bg-white mt-4">
          <View className="p-4 border-b border-gray-200">
            <Text className="text-lg font-semibold text-gray-800">Notification Settings</Text>
          </View>

          <View className="p-4">
            {notificationSettings.map((setting) => (
              <View
                key={setting.type}
                className="flex-row items-center justify-between py-4 border-b border-gray-100"
              >
                <View>
                  <Text className="text-base font-medium text-gray-800 capitalize">
                    {setting.type} Notifications
                  </Text>
                  <Text className="text-sm text-gray-500 mt-1">{setting.description}</Text>
                </View>
                <Switch
                  value={setting.enabled}
                  onValueChange={() => handleNotificationToggle(setting.type)}
                  trackColor={{ false: '#D1D5DB', true: '#93C5FD' }}
                  thumbColor={setting.enabled ? '#10B981' : '#F3F4F6'}
                  ios_backgroundColor="#D1D5DB"
                />
              </View>
            ))}
          </View>
        </View>

        {/* Send Notifications Section (Mock) */}
        <View className="bg-white mt-4">
          <View className="p-4 border-b border-gray-200">
            <Text className="text-lg font-semibold text-gray-800">Send Notifications</Text>
          </View>

          <View className="p-4 space-y-4">
            <TouchableOpacity
              onPress={handleSendTeacherNotification}
              className="bg-blue-500 p-4 rounded-lg"
              style={{
                elevation: 2,
                shadowColor: '#3B82F6',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.2,
                shadowRadius: 2,
              }}
              activeOpacity={0.8}
            >
              <View className="flex-row justify-center items-center">
                <FontAwesome name="graduation-cap" size={18} color="white" style={{ marginRight: 8 }} />
                <Text className="text-white text-center font-medium text-base">Send to Teachers</Text>
              </View>
            </TouchableOpacity>

            {/* <TouchableOpacity
              onPress={handleSendParentNotification}
              className="bg-green-500 p-4 rounded-lg"
              style={{
                elevation: 2,
                shadowColor: '#10B981',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.2,
                shadowRadius: 2,
              }}
              activeOpacity={0.8}
            >
              <View className="flex-row justify-center items-center">
                <FontAwesome name="users" size={18} color="white" style={{ marginRight: 8 }} />
                <Text className="text-white text-center font-medium text-base">Send to Parents</Text>
              </View>
            </TouchableOpacity> */}
          </View>
        </View>

        {/* Sign Out */}
        <View className="px-4 mt-6 space-y-4">
          <TouchableOpacity
            className="bg-red-500 p-4 rounded-lg"
            onPress={handleDirectSignOut}
            style={{
              elevation: 2,
              shadowColor: '#EF4444',
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: 0.2,
              shadowRadius: 2,
            }}
            activeOpacity={0.8}
          >
            <View className="flex-row justify-center items-center">
              <FontAwesome name="sign-out" size={18} color="white" style={{ marginRight: 8 }} />
              <Text className="text-white text-center font-medium text-base">Sign Out</Text>
            </View>
          </TouchableOpacity>

          {/* Removed redundant Direct Sign Out Button */}
        </View>

        {/* Footer */}
        <View className="p-4 items-center mb-24">
          <Text className="text-sm text-gray-500">Version 1.0.0</Text>
        </View>
      </ScrollView>
    </View>
  );
}
