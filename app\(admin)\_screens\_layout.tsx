import { Stack } from 'expo-router';
import { View, Text, TouchableOpacity } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

// Custom header component with logo and back button
function CustomHeader({ title }: { title: string }) {
  const router = useRouter();

  return (
    <View className="flex-row items-center bg-white px-4 py-3 border-b border-gray-200">
      <TouchableOpacity
        onPress={() => router.back()}
        className="mr-3"
      >
        <FontAwesome name="arrow-left" size={20} color="#3b82f6" />
      </TouchableOpacity>

      <View className="w-8 h-8 bg-blue-500 rounded-lg items-center justify-center mr-2">
        <FontAwesome name="graduation-cap" size={16} color="white" />
      </View>

      <Text className="text-lg font-bold text-gray-800 flex-1">{title}</Text>
    </View>
  );
}

export default function ScreensLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        tabBarStyle: { display: 'none' }, // This hides the bottom tab bar
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen
        name="payments"
        options={{
          header: () => <CustomHeader title="Payments" />,
        }}
      />
      <Stack.Screen
        name="notifications"
        options={{
          header: () => <CustomHeader title="Notifications" />,
        }}
      />
      <Stack.Screen
        name="settings"
        options={{
          header: () => <CustomHeader title="Settings" />,
        }}
      />
      <Stack.Screen
        name="user-management"
        options={{
          header: () => <CustomHeader title="User Management" />,
        }}
      />
    </Stack>
  );
}
