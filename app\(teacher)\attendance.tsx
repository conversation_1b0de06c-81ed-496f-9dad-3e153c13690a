import { FontAwesome } from '@expo/vector-icons';
import { collection, doc, getDoc, getDocs, query, setDoc, where } from 'firebase/firestore';
import { useEffect, useState } from 'react';
import { ActivityIndicator, Alert, Modal, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { Calendar } from 'react-native-calendars';
import { firestore } from '../../config/firebase';
import { useAuth } from '../../hooks/useAuth';

interface Student {
  id: string;
  name: string;
  rollNumber: string;
  class: string;
  attendance?: Record<string, 'present' | 'absent'>;
}

interface CalendarDay {
  dateString: string;
  day: number;
  month: number;
  year: number;
  timestamp: number;
}

export default function TeacherAttendance() {
  const { user } = useAuth();
  const [selectedDate, setSelectedDate] = useState(
    new Date().toISOString().split('T')[0]
  );
  const [showDateModal, setShowDateModal] = useState(false);
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [teacherClass, setTeacherClass] = useState<string>('');
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [availableClasses, setAvailableClasses] = useState<string[]>([]);
  const [attendanceData, setAttendanceData] = useState<Record<string, Record<string, 'present' | 'absent'>>>({});
  // Removed unused state
  const [showClassModal, setShowClassModal] = useState(false);

  // Fetch teacher profile and available classes
  useEffect(() => {
    fetchTeacherProfile();
    fetchAvailableClasses();
  }, []);

  const fetchTeacherProfile = async () => {
    if (!user?.id) {
      Alert.alert('Error', 'User not authenticated');
      setLoading(false);
      return;
    }

    try {
      console.log('Fetching teacher profile...');
      const teachersQuery = query(collection(firestore, 'teachers'), where('userId', '==', user ? user.id : ''));
      const querySnapshot = await getDocs(teachersQuery);

      if (!querySnapshot.empty) {
        const teacherData = querySnapshot.docs[0].data();
        const assignedClass = teacherData.classTeacher || '';
        console.log('Teacher is assigned to class:', assignedClass);
        setTeacherClass(assignedClass);

        // Set the assigned class as default selected class
        if (assignedClass) {
          setSelectedClass(assignedClass);
        }
      } else {
        console.log('No teacher profile found');
      }
    } catch (error) {
      console.error('Error fetching teacher profile:', error);
      Alert.alert('Error', 'Failed to load teacher profile');
    } finally {
      setLoading(false);
    }
  };

  // Fetch all available classes
  const fetchAvailableClasses = async () => {
    try {
      console.log('Fetching available classes...');
      const studentsCollection = collection(firestore, 'students');
      const studentsQuery = query(studentsCollection, where('status', '==', 'active'));
      const querySnapshot = await getDocs(studentsQuery);

      // Extract unique class values
      const classSet = new Set<string>();
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.class) {
          classSet.add(data.class);
        }
      });

      const classes = Array.from(classSet).sort();
      console.log(`Found ${classes.length} unique classes:`, classes);
      setAvailableClasses(classes);
    } catch (error) {
      console.error('Error fetching classes:', error);
      Alert.alert('Error', 'Failed to load classes');
    }
  };

  // Handle class selection
  const handleClassSelect = (classValue: string) => {
    setSelectedClass(classValue);
    // Removed setClassSelected call
    fetchStudents(classValue);
  };

  // Fetch students for the selected class
  const fetchStudents = async (classValue: string) => {
    try {
      setLoading(true);
      console.log('Fetching students for class:', classValue);
      const studentsQuery = query(collection(firestore, 'students'), where('class', '==', classValue), where('status', '==', 'active'));
      const querySnapshot = await getDocs(studentsQuery);

      const studentsData: Student[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        studentsData.push({
          id: doc.id,
          name: data.name || 'Unknown',
          rollNumber: data.rollNumber || '',
          class: data.class || '',
        });
      });

      console.log(`Found ${studentsData.length} students for class ${classValue}`);
      setStudents(studentsData);

      // Now fetch attendance data for the selected date
      fetchAttendanceData(classValue, selectedDate);
    } catch (error) {
      console.error('Error fetching students:', error);
      Alert.alert('Error', 'Failed to load students');
    } finally {
      setLoading(false);
    }
  };

  // Fetch attendance data for the selected date
  const fetchAttendanceData = async (classValue: string, date: string) => {
    try {
      console.log(`Fetching attendance data for class ${classValue} on ${date}`);
      const attendanceRef = doc(firestore, 'attendance', `${classValue}_${date}`);
      const attendanceDoc = await getDoc(attendanceRef);

      if (attendanceDoc.exists()) {
        const data = attendanceDoc.data();
        console.log('Attendance data found:', data);
        setAttendanceData({ ...attendanceData, [date]: data.students || {} });
      } else {
        console.log('No attendance data found for this date');
        // Initialize empty attendance data for this date
        setAttendanceData({ ...attendanceData, [date]: {} });
      }
    } catch (error) {
      console.error('Error fetching attendance data:', error);
      Alert.alert('Error', 'Failed to load attendance data');
    } finally {
      setLoading(false);
    }
  };

  // When date changes, fetch attendance data for that date
  useEffect(() => {
    if (teacherClass) {
      setLoading(true);
      fetchAttendanceData(teacherClass, selectedDate);
    }
  }, [selectedDate]);

  const toggleAttendance = (studentId: string) => {
    const currentDateAttendance = attendanceData[selectedDate] || {};
    const currentStatus = currentDateAttendance[studentId];

    // Update attendance data
    const newAttendanceData = {
      ...attendanceData,
      [selectedDate]: {
        ...currentDateAttendance,
        [studentId]: currentStatus === 'present' ? 'absent' : 'present'
      }
    };

    setAttendanceData(newAttendanceData);
  };

  const getAttendanceStatus = (studentId: string) => {
    const currentDateAttendance = attendanceData[selectedDate] || {};
    return currentDateAttendance[studentId] || 'absent';
  };

  const getAttendanceSummary = () => {
    const total = students.length;
    const currentDateAttendance = attendanceData[selectedDate] || {};

    // Count present students
    let present = 0;
    students.forEach(student => {
      if (currentDateAttendance[student.id] === 'present') {
        present++;
      }
    });

    return {
      total,
      present,
      absent: total - present,
      percentage: total > 0 ? ((present / total) * 100).toFixed(1) : '0.0',
    };
  };

  const handleSubmit = async () => {
    // Only use selectedClass - don't require teacherClass
    if (!selectedClass) {
      Alert.alert('Error', 'Please select a class first');
      return;
    }

    const classToUse = selectedClass;

    try {
      setSubmitting(true);
      console.log(`Saving attendance for class ${classToUse} on ${selectedDate}`);

      // Get the current date attendance data
      const currentDateAttendance = attendanceData[selectedDate] || {};

      // Create attendance document with HARDCODED submittedBy
      // This is a direct fix for the mobile issue
      const attendanceRef = doc(firestore, 'attendance', `${classToUse}_${selectedDate}`);

      // Create a document with a hardcoded submittedBy value
      // This ensures it will never be undefined
      const docData = {
        class: classToUse,
        date: selectedDate,
        students: currentDateAttendance,
        submittedBy: "mobile-user-fixed",  // HARDCODED value to fix the issue
        submittedAt: new Date().toISOString(),
      };

      // Save the document
      await setDoc(attendanceRef, docData);

      console.log('Attendance saved successfully');
      Alert.alert('Success', 'Attendance submitted successfully');
    } catch (error) {
      console.error('Error saving attendance:', error);
      Alert.alert('Error', 'Failed to save attendance: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setSubmitting(false);
    }
  };

  const summary = getAttendanceSummary();

  return (
    <ScrollView className="flex-1 bg-gray-50">
      {/* Header with Logo */}
      <View className="bg-white p-4 border-b border-gray-200">
        <View className="flex-row justify-between items-center">
          <View>
            <Text className="text-xl font-bold text-gray-800">Attendance</Text>
            {teacherClass ? (
              <Text className="text-sm text-blue-600">Your Class: {teacherClass}</Text>
            ) : (
              <Text className="text-sm text-orange-600">Please select a class to take attendance</Text>
            )}
          </View>
          <View className="h-10 w-10 bg-blue-100 rounded-full items-center justify-center">
            <FontAwesome name="graduation-cap" size={20} color="#3B82F6" />
          </View>
        </View>
      </View>

      {loading ? (
        <View className="flex-1 items-center justify-center p-10">
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text className="mt-4 text-gray-600">Loading attendance data...</Text>
        </View>
      ) : (
        <>
          {/* Class Selection Dropdown */}
          <View className="mx-4 mb-4 bg-white p-5 rounded-xl shadow-md" style={{
            elevation: 3,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 3,
          }}>
            <View className="flex-row items-center mb-3">
              <View className="w-8 h-8 bg-blue-100 rounded-full items-center justify-center mr-2">
                <FontAwesome name="users" size={16} color="#3B82F6" />
              </View>
              <Text className="text-lg font-bold text-gray-800">Select Class</Text>
            </View>
            <TouchableOpacity
              className="flex-row justify-between items-center bg-gray-100 p-4 rounded-xl border border-gray-200"
              onPress={() => setShowClassModal(true)}
              style={{
                elevation: 1,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.05,
                shadowRadius: 1,
              }}
            >
              <View className="flex-row items-center">
                {selectedClass ? (
                  <View className="w-8 h-8 bg-blue-500 rounded-full items-center justify-center mr-3">
                    <Text className="text-white font-bold">{selectedClass.charAt(0)}</Text>
                  </View>
                ) : (
                  <View className="w-8 h-8 bg-gray-300 rounded-full items-center justify-center mr-3">
                    <FontAwesome name="graduation-cap" size={14} color="#6B7280" />
                  </View>
                )}
                <Text className="text-gray-800 font-medium text-base">
                  {selectedClass ? `Class ${selectedClass}` : 'Select a class *'}
                  {selectedClass === teacherClass && teacherClass ? ' (Your Class)' : ''}
                </Text>
              </View>
              <View className="bg-blue-100 w-8 h-8 rounded-full items-center justify-center">
                <FontAwesome name="chevron-down" size={14} color="#3B82F6" />
              </View>
            </TouchableOpacity>
          </View>

          {/* Class Selection Modal */}
          <Modal
            visible={showClassModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => setShowClassModal(false)}
          >
            <View className="flex-1 bg-black/60 justify-center">
              <View className="bg-white mx-4 rounded-2xl overflow-hidden shadow-xl" style={{
                elevation: 10,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 5 },
                shadowOpacity: 0.3,
                shadowRadius: 10,
              }}>
                <View className="p-5 border-b border-gray-200 bg-blue-50">
                  <View className="flex-row items-center justify-between">
                    <View className="flex-row items-center">
                      <View className="w-10 h-10 bg-blue-500 rounded-full items-center justify-center mr-3">
                        <FontAwesome name="users" size={18} color="white" />
                      </View>
                      <Text className="text-xl font-bold text-gray-800">Select Class</Text>
                    </View>
                    <TouchableOpacity
                      className="w-8 h-8 bg-gray-200 rounded-full items-center justify-center"
                      onPress={() => setShowClassModal(false)}
                    >
                      <FontAwesome name="times" size={16} color="#6B7280" />
                    </TouchableOpacity>
                  </View>
                </View>

                <ScrollView style={{ maxHeight: 350 }}>
                  <View className="p-3">
                    {availableClasses.map((classItem) => (
                      <TouchableOpacity
                        key={classItem}
                        className={`mb-3 p-4 rounded-xl flex-row justify-between items-center ${selectedClass === classItem ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 border border-gray-100'}`}
                        style={{
                          elevation: selectedClass === classItem ? 2 : 0,
                          shadowColor: '#000',
                          shadowOffset: { width: 0, height: 1 },
                          shadowOpacity: selectedClass === classItem ? 0.1 : 0,
                          shadowRadius: 2,
                        }}
                        onPress={() => {
                          handleClassSelect(classItem);
                          setShowClassModal(false);
                        }}
                      >
                        <View className="flex-row items-center flex-1">
                          <View
                            className={`w-12 h-12 rounded-full items-center justify-center mr-4 ${classItem === teacherClass ? 'bg-blue-500' : (selectedClass === classItem ? 'bg-blue-400' : 'bg-gray-300')}`}
                          >
                            <Text className="font-bold text-white text-lg">
                              {classItem.charAt(0)}
                            </Text>
                          </View>
                          <View>
                            <Text className={`text-base font-bold ${classItem === teacherClass ? 'text-blue-600' : (selectedClass === classItem ? 'text-blue-500' : 'text-gray-800')}`}>
                              Class {classItem}
                            </Text>
                            {classItem === teacherClass && (
                              <View className="bg-blue-100 px-2 py-0.5 rounded-full mt-1 self-start">
                                <Text className="text-xs text-blue-600 font-medium">Your Class</Text>
                              </View>
                            )}
                          </View>
                        </View>
                        {classItem === selectedClass && (
                          <View className="bg-blue-500 w-8 h-8 rounded-full items-center justify-center">
                            <FontAwesome name="check" size={14} color="white" />
                          </View>
                        )}
                      </TouchableOpacity>
                    ))}
                  </View>
                </ScrollView>

                <View className="p-4 border-t border-gray-200 bg-gray-50">
                  <TouchableOpacity
                    className="p-4 bg-blue-500 rounded-xl flex-row items-center justify-center"
                    onPress={() => setShowClassModal(false)}
                  >
                    <FontAwesome name="check" size={16} color="white" style={{ marginRight: 8 }} />
                    <Text className="text-center font-bold text-white text-base">Confirm Selection</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>

          {/* Date Selection Modal */}
          <Modal
            visible={showDateModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => setShowDateModal(false)}
          >
            <View className="flex-1 bg-black/60 justify-center">
              <View className="bg-white mx-4 rounded-2xl overflow-hidden shadow-xl" style={{
                elevation: 10,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 5 },
                shadowOpacity: 0.3,
                shadowRadius: 10,
              }}>
                <View className="p-5 border-b border-gray-200 bg-blue-50">
                  <View className="flex-row items-center justify-between">
                    <View className="flex-row items-center">
                      <View className="w-10 h-10 bg-blue-500 rounded-full items-center justify-center mr-3">
                        <FontAwesome name="calendar" size={18} color="white" />
                      </View>
                      <Text className="text-xl font-bold text-gray-800">Select Date</Text>
                    </View>
                    <TouchableOpacity
                      className="w-8 h-8 bg-gray-200 rounded-full items-center justify-center"
                      onPress={() => setShowDateModal(false)}
                    >
                      <FontAwesome name="times" size={16} color="#6B7280" />
                    </TouchableOpacity>
                  </View>
                </View>

                <View className="p-4">
                  <Calendar
                    onDayPress={(day: CalendarDay) => {
                      setSelectedDate(day.dateString);
                      if (selectedClass) {
                        fetchAttendanceData(selectedClass, day.dateString);
                      }
                      setShowDateModal(false);
                    }}
                    markedDates={{
                      [selectedDate]: { selected: true, selectedColor: '#3B82F6' }
                    }}
                    theme={{
                      todayTextColor: '#3B82F6',
                      arrowColor: '#3B82F6',
                      dotColor: '#3B82F6',
                      selectedDayBackgroundColor: '#3B82F6',
                    }}
                  />
                </View>

                <View className="p-4 border-t border-gray-200 bg-gray-50">
                  <TouchableOpacity
                    className="p-4 bg-blue-500 rounded-xl flex-row items-center justify-center"
                    onPress={() => setShowDateModal(false)}
                  >
                    <FontAwesome name="check" size={16} color="white" style={{ marginRight: 8 }} />
                    <Text className="text-center font-bold text-white text-base">Confirm Selection</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>

          {/* Date Selection */}
          <View className="mx-4 mb-4 bg-white p-5 rounded-xl shadow-md" style={{
            elevation: 3,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 3,
          }}>
            <View className="flex-row items-center mb-3">
              <View className="w-8 h-8 bg-blue-100 rounded-full items-center justify-center mr-2">
                <FontAwesome name="calendar" size={16} color="#3B82F6" />
              </View>
              <Text className="text-lg font-bold text-gray-800">Select Date</Text>
            </View>

            <TouchableOpacity
              className="flex-row justify-between items-center bg-gray-100 p-4 rounded-xl border border-gray-200"
              onPress={() => setShowDateModal(true)}
              style={{
                elevation: 1,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.05,
                shadowRadius: 1,
              }}
            >
              <View className="flex-row items-center">
                <View className="w-10 h-10 bg-blue-500 rounded-full items-center justify-center mr-3">
                  <FontAwesome name="calendar-check-o" size={16} color="white" />
                </View>
                <View>
                  <Text className="text-gray-500 text-sm">Selected Date</Text>
                  <Text className="text-base font-bold text-gray-800">
                    {new Date(selectedDate).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
                  </Text>
                </View>
              </View>
              <View className="bg-blue-100 w-8 h-8 rounded-full items-center justify-center">
                <FontAwesome name="chevron-down" size={14} color="#3B82F6" />
              </View>
            </TouchableOpacity>
          </View>

          {/* Attendance Summary */}
          <View className="mx-4 mb-4 bg-white p-4 rounded-xl shadow-sm">
            <Text className="text-lg font-semibold mb-3 text-gray-800">Attendance Summary</Text>
            <View className="flex-row justify-between">
              <View className="items-center bg-gray-50 p-3 rounded-lg flex-1 mr-2">
                <Text className="text-gray-600 text-xs mb-1">Total</Text>
                <Text className="text-xl font-bold text-gray-800">{summary.total}</Text>
              </View>
              <View className="items-center bg-green-50 p-3 rounded-lg flex-1 mr-2">
                <Text className="text-green-600 text-xs mb-1">Present</Text>
                <Text className="text-xl font-bold text-green-600">
                  {summary.present}
                </Text>
              </View>
              <View className="items-center bg-red-50 p-3 rounded-lg flex-1 mr-2">
                <Text className="text-red-600 text-xs mb-1">Absent</Text>
                <Text className="text-xl font-bold text-red-600">
                  {summary.absent}
                </Text>
              </View>
              <View className="items-center bg-blue-50 p-3 rounded-lg flex-1">
                <Text className="text-blue-600 text-xs mb-1">%</Text>
                <Text className="text-xl font-bold text-blue-600">
                  {summary.percentage}%
                </Text>
              </View>
            </View>
          </View>

          {/* Selected Date */}
          <View className="mx-4 mb-2 flex-row items-center">
            <FontAwesome name="calendar" size={16} color="#6B7280" />
            <Text className="ml-2 text-gray-600">
              {new Date(selectedDate).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </Text>
          </View>

          {/* Student List */}
          <View className="mx-4 mb-4">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-lg font-semibold text-gray-800">Mark Attendance</Text>
              <Text className="text-sm text-gray-500">{students.length} Students</Text>
            </View>

            {students.length === 0 ? (
              <View className="items-center justify-center py-8 bg-white rounded-xl p-4">
                <FontAwesome name="users" size={40} color="#D1D5DB" />
                <Text className="text-gray-500 mt-2 text-center">No students found for this class</Text>
              </View>
            ) : (
              students.map((student) => (
                <TouchableOpacity
                  key={student.id}
                  className="mb-3 p-4 bg-white border border-gray-100 rounded-xl shadow-sm"
                  onPress={() => toggleAttendance(student.id)}
                  activeOpacity={0.7}
                >
                  <View className="flex-row justify-between items-center">
                    <View>
                      <Text className="font-medium text-gray-800">{student.name}</Text>
                      <Text className="text-gray-500">Roll No: {student.rollNumber}</Text>
                    </View>
                    <View
                      className={`px-3 py-1 rounded-full ${
                        getAttendanceStatus(student.id) === 'present'
                          ? 'bg-green-100'
                          : 'bg-red-100'
                      }`}
                    >
                      <Text
                        className={`text-sm capitalize ${
                          getAttendanceStatus(student.id) === 'present'
                            ? 'text-green-800'
                            : 'text-red-800'
                        }`}
                      >
                        {getAttendanceStatus(student.id)}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              ))
            )}

            {/* Submit Button */}
            <TouchableOpacity
              className={`mt-4 p-4 rounded-xl ${submitting ? 'bg-blue-400' : students.length === 0 ? 'bg-gray-400' : 'bg-blue-500'}`}
              onPress={handleSubmit}
              disabled={submitting || students.length === 0}
            >
              {submitting ? (
                <View className="flex-row justify-center items-center">
                  <ActivityIndicator size="small" color="white" />
                  <Text className="text-white text-center font-bold ml-2">
                    Submitting...
                  </Text>
                </View>
              ) : students.length === 0 ? (
                <Text className="text-white text-center font-bold">
                  No Students to Mark
                </Text>
              ) : (
                <Text className="text-white text-center font-bold">
                  Submit Attendance
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </>
      )}
    </ScrollView>
  );
}