import { Tabs } from 'expo-router';
import { useAuth } from '../../hooks/useAuth';
import { FontAwesome } from '@expo/vector-icons';
import { useWindowDimensions, Text, View } from 'react-native';

interface TabBarIconProps {
  color: string;
  size: number;
  focused?: boolean;
}

interface TabBarLabelProps {
  focused: boolean;
  color: string;
}

export default function ParentLayout() {
  const { user } = useAuth();
  const { width } = useWindowDimensions();
  const isMobile = width < 768;

  if (!user || user.role !== 'parent') {
    return null;
  }

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#3b82f6',
        tabBarInactiveTintColor: '#6b7280',
        headerShown: false,
        tabBarStyle: {
          height: isMobile ? 70 : 80,
          paddingBottom: 10,
          paddingTop: 10,
          borderTopWidth: 0,
          elevation: 10,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -3 },
          shadowOpacity: 0.15,
          shadowRadius: 5,
          backgroundColor: 'white',
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          borderTopLeftRadius: 25,
          borderTopRightRadius: 25,
          justifyContent: 'space-evenly',
          alignItems: 'center',
          paddingHorizontal: 20,
        },
        tabBarItemStyle: {
          paddingVertical: 5,
          height: isMobile ? 50 : 60,
          width: isMobile ? 70 : 80,
          justifyContent: 'center',
          alignItems: 'center',
          marginHorizontal: 10,
          borderRadius: 15,
        },
        tabBarLabelPosition: 'below-icon',
        tabBarAllowFontScaling: false,
        tabBarLabelStyle: {
          fontSize: isMobile ? 10 : 12,
          marginTop: 2,
          fontWeight: '600',
          opacity: 1,
          color: '#6b7280',
          display: 'flex',
          textAlign: 'center',
        },
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: 'Home',
          tabBarLabel: ({ focused }: TabBarLabelProps) => (
            <View style={{
              backgroundColor: focused ? '#3b82f620' : 'transparent',
              paddingHorizontal: 8,
              paddingVertical: 2,
              borderRadius: 10,
            }}>
              <Text style={{ color: focused ? '#3b82f6' : '#6b7280', fontSize: 11, fontWeight: '600', marginTop: 3 }}>
                Home
              </Text>
            </View>
          ),
          tabBarIcon: ({ color, size }: TabBarIconProps) => (
            <FontAwesome name="home" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="attendance"
        options={{
          title: 'Attendance',
          tabBarLabel: ({ focused }: TabBarLabelProps) => (
            <View style={{
              backgroundColor: focused ? '#3b82f620' : 'transparent',
              paddingHorizontal: 8,
              paddingVertical: 2,
              borderRadius: 10,
            }}>
              <Text style={{ color: focused ? '#3b82f6' : '#6b7280', fontSize: 11, fontWeight: '600', marginTop: 3 }}>
                Attendance
              </Text>
            </View>
          ),
          tabBarIcon: ({ color, size }: TabBarIconProps) => (
            <FontAwesome name="calendar-check-o" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="payment"
        options={{
          title: 'Payment',
          tabBarLabel: ({ focused }: TabBarLabelProps) => (
            <View style={{
              backgroundColor: focused ? '#3b82f620' : 'transparent',
              paddingHorizontal: 8,
              paddingVertical: 2,
              borderRadius: 10,
            }}>
              <Text style={{ color: focused ? '#3b82f6' : '#6b7280', fontSize: 11, fontWeight: '600', marginTop: 3 }}>
                Payment
              </Text>
            </View>
          ),
          tabBarIcon: ({ color, size }: TabBarIconProps) => (
            <FontAwesome name="money" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="calendar"
        options={{
          title: 'Calendar',
          tabBarLabel: ({ focused }: TabBarLabelProps) => (
            <View style={{
              backgroundColor: focused ? '#3b82f620' : 'transparent',
              paddingHorizontal: 8,
              paddingVertical: 2,
              borderRadius: 10,
            }}>
              <Text style={{ color: focused ? '#3b82f6' : '#6b7280', fontSize: 11, fontWeight: '600', marginTop: 3 }}>
                Calendar
              </Text>
            </View>
          ),
          tabBarIcon: ({ color, size }: TabBarIconProps) => (
            <FontAwesome name="calendar" size={size} color={color} />
          ),
        }}
      />
      {/* Hidden screens that are not shown in the tab bar */}
      <Tabs.Screen
        name="profile"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="contact"
        options={{
          href: null,
        }}
      />
    </Tabs>
  );
}