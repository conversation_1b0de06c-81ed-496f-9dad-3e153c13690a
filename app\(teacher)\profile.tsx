import { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { useAuth } from '../../hooks/useAuth';
import { firestore } from '../../config/firebase';
import { doc, getDoc, updateDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { FontAwesome } from '@expo/vector-icons';
import { router } from 'expo-router';

interface TeacherProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  subject: string;
  classTeacher: string;
  qualification: string;
  experience: string;
  joiningDate: string;
}

export default function TeacherProfile() {
  const { user, signOut } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');

  const [profile, setProfile] = useState<TeacherProfile>({
    id: '',
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || '',
    subject: '',
    classTeacher: '',
    qualification: '',
    experience: '',
    joiningDate: '',
  });

  const [editedProfile, setEditedProfile] = useState<TeacherProfile>(profile);

  // Fetch teacher profile from Firestore
  useEffect(() => {
    console.log('Teacher profile component mounted, user:', user);
    fetchTeacherProfile();
  }, []);

  const fetchTeacherProfile = async () => {
    if (!user?.id) {
      setError('User not authenticated');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError('');

      console.log('Fetching teacher profile from Firestore for user ID:', user.id);
      console.log('User object:', JSON.stringify(user));

      // First try to get the teacher document by user ID
      const teachersCollection = collection(firestore, 'teachers');
      const teachersQuery = query(teachersCollection, where('userId', '==', user.id));
      const querySnapshot = await getDocs(teachersQuery);
      console.log('Teacher query results:', querySnapshot.size, 'documents found');

      if (!querySnapshot.empty) {
        // Found teacher profile
        const teacherDoc = querySnapshot.docs[0];
        const teacherData = teacherDoc.data();
        console.log('Teacher data from Firestore:', JSON.stringify(teacherData));

        const teacherProfile: TeacherProfile = {
          id: teacherDoc.id,
          name: teacherData.name || user?.name || '',
          email: teacherData.email || user?.email || '',
          phone: teacherData.phone || user?.phone || '',
          subject: teacherData.subject || '',
          classTeacher: teacherData.classTeacher || '',
          qualification: teacherData.qualification || '',
          experience: teacherData.experience || '',
          joiningDate: teacherData.joiningDate || '',
        };

        console.log('Teacher profile found:', teacherProfile);
        setProfile(teacherProfile);
        setEditedProfile(teacherProfile);
      } else {
        // No teacher profile found, create a default one
        console.log('No teacher profile found, using default values');
        const defaultProfile: TeacherProfile = {
          id: '',
          name: user?.name || '',
          email: user?.email || '',
          phone: user?.phone || '',
          subject: '',
          classTeacher: '',
          qualification: '',
          experience: '',
          joiningDate: new Date().toISOString().split('T')[0],
        };

        setProfile(defaultProfile);
        setEditedProfile(defaultProfile);
      }
    } catch (error) {
      console.error('Error fetching teacher profile:', error);
      setError('Failed to load profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!user?.id) {
      Alert.alert('Error', 'User not authenticated');
      return;
    }

    try {
      setSaving(true);
      console.log('Saving teacher profile to Firestore...');

      const teacherData = {
        userId: user.id,
        name: editedProfile.name,
        email: editedProfile.email,
        phone: editedProfile.phone,
        subject: editedProfile.subject,
        classTeacher: editedProfile.classTeacher,
        qualification: editedProfile.qualification,
        experience: editedProfile.experience,
        joiningDate: editedProfile.joiningDate,
        updatedAt: new Date().toISOString(),
      };

      if (profile.id) {
        // Update existing profile
        const teacherRef = doc(firestore, 'teachers', profile.id);
        await updateDoc(teacherRef, teacherData);
      } else {
        // Create new profile
        const teachersCollection = collection(firestore, 'teachers');
        const teachersQuery = query(teachersCollection, where('userId', '==', user.id));
        const querySnapshot = await getDocs(teachersQuery);

        if (!querySnapshot.empty) {
          // Update existing profile that was found
          const teacherDoc = querySnapshot.docs[0];
          const teacherRef = doc(firestore, 'teachers', teacherDoc.id);
          await updateDoc(teacherRef, teacherData);

          // Update local profile ID
          editedProfile.id = teacherDoc.id;
        } else {
          // Create new profile (this would typically be done by an admin)
          Alert.alert('Info', 'Please contact admin to create your profile');
        }
      }

      setProfile(editedProfile);
      setIsEditing(false);

      // Show success message with specific mention of phone number if it was added/updated
      const wasPhoneAdded = !profile.phone && editedProfile.phone;
      const wasPhoneChanged = profile.phone && profile.phone !== editedProfile.phone;

      if (wasPhoneAdded) {
        Alert.alert('Success', 'Profile updated successfully. Your phone number has been added and will be visible to the admin.');
      } else if (wasPhoneChanged) {
        Alert.alert('Success', 'Profile updated successfully. Your phone number has been updated.');
      } else {
        Alert.alert('Success', 'Profile updated successfully');
      }
    } catch (error) {
      console.error('Error saving teacher profile:', error);
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditedProfile(profile);
    setIsEditing(false);
  };

  return (
    <ScrollView className="flex-1 bg-gray-50">
      {/* Page Title with Edit/Save/Cancel Buttons */}
      <View className="pt-6 px-5 mb-4 border-b border-gray-200 pb-4">
        <View className="flex-row justify-between items-center">
          <Text className="text-2xl font-bold text-gray-800">My Profile</Text>
          {!isEditing ? (
            <TouchableOpacity
              className="bg-blue-500 px-5 py-2.5 rounded-lg flex-row items-center shadow-sm"
              style={{
                elevation: 2,
                shadowColor: '#3B82F6',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.2,
                shadowRadius: 2,
              }}
              onPress={() => setIsEditing(true)}
            >
              <FontAwesome name="pencil" size={16} color="white" style={{ marginRight: 8 }} />
              <Text className="text-white font-medium text-base">Edit</Text>
            </TouchableOpacity>
          ) : (
            <View className="flex-row space-x-3">
              <TouchableOpacity
                className="bg-gray-100 px-5 py-2.5 rounded-lg flex-row items-center border border-gray-300"
                onPress={handleCancel}
              >
                <Text className="text-gray-700 font-medium text-base">Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                className="bg-blue-500 px-5 py-2.5 rounded-lg flex-row items-center shadow-sm"
                style={{
                  elevation: 2,
                  shadowColor: '#3B82F6',
                  shadowOffset: { width: 0, height: 1 },
                  shadowOpacity: 0.2,
                  shadowRadius: 2,
                }}
                onPress={handleSave}
              >
                <Text className="text-white font-medium text-base">Save</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>

      <View className="p-4">
        {/* User Info Card */}
        {/* <View className="bg-white p-6 rounded-xl shadow-sm mb-6 border border-gray-100">
          <View className="flex-row items-center mb-5">
            <View className="w-20 h-20 bg-blue-100 rounded-full items-center justify-center mr-5 border-2 border-blue-200">
              <FontAwesome name="user-circle" size={50} color="#3B82F6" />
            </View>
            <View>
              <Text className="text-gray-800 text-xl font-bold">{profile.name || 'Teacher'}</Text>
              <Text className="text-gray-500 mt-1">{profile.email || 'Loading...'}</Text>
              {profile.phone && (
                <View className="flex-row items-center mt-2">
                  <FontAwesome name="phone" size={14} color="#6B7280" style={{ marginRight: 6 }} />
                  <Text className="text-gray-600 font-medium">{profile.phone}</Text>
                </View>
              )}
            </View>
          </View>

          <View className="flex-row flex-wrap mt-3 pt-3 border-t border-gray-100">
            <View className="bg-blue-50 px-4 py-2 rounded-full mr-2 mb-2 flex-row items-center">
              <FontAwesome name="graduation-cap" size={14} color="#3B82F6" style={{ marginRight: 6 }} />
              <Text className="text-blue-600 font-medium">{profile.subject || 'Subject: Not assigned'}</Text>
            </View>
            <View className="bg-green-50 px-4 py-2 rounded-full mr-2 mb-2 flex-row items-center">
              <FontAwesome name="users" size={14} color="#10B981" style={{ marginRight: 6 }} />
              <Text className="text-green-600 font-medium">Class: {profile.classTeacher || 'Not assigned'}</Text>
            </View>
            {profile.joiningDate ? (
              <View className="bg-purple-50 px-4 py-2 rounded-full mr-2 mb-2 flex-row items-center">
                <FontAwesome name="calendar" size={14} color="#8B5CF6" style={{ marginRight: 6 }} />
                <Text className="text-purple-600 font-medium">Joined: {profile.joiningDate}</Text>
              </View>
            ) : null}
          </View>
        </View> */}

        {/* Basic Information */}
      <View className="mb-6 bg-white p-5 rounded-xl shadow-md" style={{
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      }}>
        <View className="flex-row items-center mb-4">
          <View className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center mr-3">
            <FontAwesome name="user" size={18} color="#3B82F6" />
          </View>
          <Text className="text-lg font-bold text-gray-800">Basic Information</Text>
        </View>

        <View className="space-y-4 bg-gray-50 p-3 rounded-lg">
          <Text className="text-blue-500 font-medium text-sm mb-2">Personal Details</Text>
          <View className="bg-gray-50 p-3 rounded-lg border border-gray-100">
            <Text className="text-gray-500 text-xs mb-1 uppercase font-medium">Full Name</Text>
            {isEditing ? (
              <TextInput
                className="p-3 bg-white border border-gray-300 rounded-lg mt-1"
                value={editedProfile.name}
                onChangeText={(text) =>
                  setEditedProfile({ ...editedProfile, name: text })
                }
                placeholder="Enter your full name"
                style={{
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: '#E5E7EB',
                  backgroundColor: '#F9FAFB'
                }}
              />
            ) : (
              <Text className="text-gray-800 text-lg font-medium">{profile.name || 'Not provided'}</Text>
            )}
          </View>

          <View className="bg-gray-50 p-3 rounded-lg border border-gray-100">
            <Text className="text-gray-500 text-xs mb-1 uppercase font-medium">Email</Text>
            <Text className="text-gray-800 text-lg font-medium">{profile.email || 'Not provided'}</Text>
          </View>

          <View className="bg-gray-50 p-3 rounded-lg border border-gray-100">
            <View className="flex-row justify-between items-center">
              <Text className="text-gray-500 text-xs mb-1 uppercase font-medium">Phone</Text>
              {/* {!isEditing && !profile.phone && (
                <Text className="text-xs text-red-500 font-medium">* Required by admin</Text>
              )} */}
            </View>
            {isEditing ? (
              <TextInput
                className="p-3 bg-white border border-gray-300 rounded-lg mt-1"
                value={editedProfile.phone}
                onChangeText={(text) =>
                  setEditedProfile({ ...editedProfile, phone: text })
                }
                keyboardType="phone-pad"
                placeholder="Enter your phone number"
                style={{
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: '#E5E7EB',
                  backgroundColor: '#F9FAFB'
                }}
              />
            ) : (
              <View className="flex-row items-center">
                <Text className="text-gray-800 text-lg font-medium">
                  {profile.phone || 'Not provided'}
                </Text>
                {!profile.phone && (
                  <TouchableOpacity
                    className="ml-3 bg-blue-100 px-3 py-1 rounded-full"
                    onPress={() => setIsEditing(true)}
                  >
                    <Text className="text-blue-600 font-medium">Add Now</Text>
                  </TouchableOpacity>
                )}
              </View>
            )}
          </View>
        </View>
      </View>

      {/* Professional Information */}
      <View className="mb-6 bg-white p-5 rounded-xl shadow-md" style={{
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      }}>
        <View className="flex-row items-center mb-4">
          <View className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center mr-3">
            <FontAwesome name="briefcase" size={18} color="#3B82F6" />
          </View>
          <Text className="text-lg font-bold text-gray-800">Professional Information</Text>
        </View>

        <View className="space-y-4 bg-gray-50 p-3 rounded-lg">
          <Text className="text-green-500 font-medium text-sm mb-2">Teaching Details</Text>
          <View className="bg-gray-50 p-3 rounded-lg border border-gray-100">
            <Text className="text-gray-500 text-xs mb-1 uppercase font-medium">Subject</Text>
            {isEditing ? (
              <TextInput
                className="p-3 bg-white border border-gray-300 rounded-lg mt-1"
                value={editedProfile.subject}
                onChangeText={(text) =>
                  setEditedProfile({ ...editedProfile, subject: text })
                }
                placeholder="Enter your subject"
                style={{
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: '#E5E7EB',
                  backgroundColor: '#F9FAFB'
                }}
              />
            ) : (
              <Text className="text-gray-800 text-lg font-medium">{profile.subject || 'Not provided'}</Text>
            )}
          </View>

          <View className="bg-gray-50 p-3 rounded-lg border border-gray-100">
            <Text className="text-gray-500 text-xs mb-1 uppercase font-medium">Class Teacher</Text>
            {isEditing ? (
              <TextInput
                className="p-3 bg-white border border-gray-300 rounded-lg mt-1"
                value={editedProfile.classTeacher}
                onChangeText={(text) =>
                  setEditedProfile({ ...editedProfile, classTeacher: text })
                }
                placeholder="Enter your class"
                style={{
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: '#E5E7EB',
                  backgroundColor: '#F9FAFB'
                }}
              />
            ) : (
              <Text className="text-gray-800 text-lg font-medium">{profile.classTeacher || 'Not provided'}</Text>
            )}
          </View>

          <View className="bg-gray-50 p-3 rounded-lg border border-gray-100">
            <Text className="text-gray-500 text-xs mb-1 uppercase font-medium">Qualification</Text>
            {isEditing ? (
              <TextInput
                className="p-3 bg-white border border-gray-300 rounded-lg mt-1"
                value={editedProfile.qualification}
                onChangeText={(text) =>
                  setEditedProfile({ ...editedProfile, qualification: text })
                }
                placeholder="Enter your qualification"
                style={{
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: '#E5E7EB',
                  backgroundColor: '#F9FAFB'
                }}
              />
            ) : (
              <Text className="text-gray-800 text-lg font-medium">{profile.qualification || 'Not provided'}</Text>
            )}
          </View>

          <View className="bg-gray-50 p-3 rounded-lg border border-gray-100">
            <Text className="text-gray-500 text-xs mb-1 uppercase font-medium">Experience</Text>
            {isEditing ? (
              <TextInput
                className="p-3 bg-white border border-gray-300 rounded-lg mt-1"
                value={editedProfile.experience}
                onChangeText={(text) =>
                  setEditedProfile({ ...editedProfile, experience: text })
                }
                placeholder="Enter your experience"
                style={{
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: '#E5E7EB',
                  backgroundColor: '#F9FAFB'
                }}
              />
            ) : (
              <Text className="text-gray-800 text-lg font-medium">{profile.experience || 'Not provided'}</Text>
            )}
          </View>

          <View className="bg-gray-50 p-3 rounded-lg border border-gray-100">
            <Text className="text-gray-500 text-xs mb-1 uppercase font-medium">Joining Date</Text>
            {isEditing ? (
              <TextInput
                className="p-3 bg-white border border-gray-300 rounded-lg mt-1"
                value={editedProfile.joiningDate}
                onChangeText={(text) =>
                  setEditedProfile({ ...editedProfile, joiningDate: text })
                }
                placeholder="YYYY-MM-DD"
                style={{
                  fontSize: 16,
                  borderWidth: 1,
                  borderColor: '#E5E7EB',
                  backgroundColor: '#F9FAFB'
                }}
              />
            ) : (
              <Text className="text-gray-800 text-lg font-medium">{profile.joiningDate || 'Not provided'}</Text>
            )}
          </View>
        </View>
      </View>

      {/* Sign Out Button - Direct Sign Out */}
      <View className="mb-32 mt-8">
        <TouchableOpacity
          className="bg-red-500 p-5 rounded-xl flex-row items-center justify-center shadow-lg"
          style={{
            elevation: 5,
            shadowColor: '#EF4444',
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 0.3,
            shadowRadius: 5,
          }}
          onPress={() => {
            // Direct sign out without confirmation
            console.log('Directly signing out...');
            signOut()
              .then(() => {
                console.log('Sign out successful');
                // Force immediate navigation to login screen
                router.replace('/(auth)/login');
              })
              .catch((error) => {
                console.error('Sign out error:', error);
                Alert.alert('Error', 'Failed to sign out. Please try again.');
              });
          }}
        >
          <FontAwesome name="sign-out" size={22} color="white" style={{ marginRight: 10 }} />
          <Text className="text-white font-bold text-lg">Sign Out</Text>
        </TouchableOpacity>
      </View>
      </View>
    </ScrollView>
  );
}