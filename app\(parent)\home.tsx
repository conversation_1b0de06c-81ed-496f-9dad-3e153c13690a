import { useState, useEffect, useCallback } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert, Modal, ActivityIndicator, Image, Dimensions, RefreshControl, StatusBar, SafeAreaView, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../../hooks/useAuth';
import { FontAwesome, MaterialIcons, Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { firestore } from '../../config/firebase';
import { collection, getDocs, query, where, doc, getDoc, addDoc } from 'firebase/firestore';
// import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';

interface Child {
  id: string;
  name: string;
  class: string;
  attendance?: {
    present: number;
    total: number;
  };
}

interface Event {
  id: string;
  title: string;
  description?: string;
  date: string;
  type: 'other' | 'holiday' | 'exam' | 'activity';
  targetAudience?: 'all' | 'parents' | 'teachers' | 'specific_class';
  isToday?: boolean;
}

interface Announcement {
  id: string;
  title: string;
  message: string;
  date: string;
  targetAudience?: 'all' | 'parents' | 'teachers' | 'students';
}

export default function ParentHome() {
  const { user } = useAuth();
  const [showChildModal, setShowChildModal] = useState(false);
  const [selectedChild, setSelectedChild] = useState<Child | null>(null);
  const [children, setChildren] = useState<Child[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [attendanceData, setAttendanceData] = useState<{[studentId: string]: {present: number, total: number}}>({});

  // Consolidated loading state
  const isLoading = loading || refreshing;

  // Fetch children data from Firestore
  useEffect(() => {
    if (user) {
      fetchChildren();
    }
  }, [user]);

  // Function to handle refresh
  const onRefresh = useCallback(async () => {
    if (loading) return; // Prevent multiple refreshes

    setRefreshing(true);
    try {
      if (user) {
        // Use Promise.all to fetch data in parallel
        await Promise.all([
          fetchChildren(),
          fetchEvents(),
          fetchAnnouncements()
        ]);
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  }, [user, loading]);

  const fetchChildren = async (): Promise<void> => {
    if (!user) return;

    try {
      setLoading(true);

      // First, get the user's phone number from Firestore
      const userRef = doc(firestore, 'users', user.id);
      const userDoc = await getDoc(userRef);
      const userPhone = userDoc.exists() ? userDoc.data().phone : null;

      const childrenCollection = collection(firestore, 'students');

      // Get students where parentPhone matches user's phone number
      // If user doesn't have a phone number, fall back to parentId
      let childrenQuery;
      if (userPhone) {
        console.log('Fetching children by phone number:', userPhone);
        childrenQuery = query(childrenCollection, where('parentPhone', '==', userPhone));
      } else {
        console.log('No phone number found, fetching by parentId');
        childrenQuery = query(childrenCollection, where('parentId', '==', user.id));
      }

      const querySnapshot = await getDocs(childrenQuery);

      const childrenData: Child[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        childrenData.push({
          id: doc.id,
          name: data.name || 'Unknown',
          class: data.class || 'Unknown',
        });
      });

      console.log(`Found ${childrenData.length} children for this parent`);
      setChildren(childrenData);

      // Try to get previously selected child from AsyncStorage
      try {
        const savedChildId = await AsyncStorage.getItem('selectedChildId');
        console.log('Retrieved saved child ID:', savedChildId);

        if (savedChildId) {
          // Find the child with the saved ID
          const savedChild = childrenData.find(child => child.id === savedChildId);
          if (savedChild) {
            console.log('Found saved child:', savedChild.name);
            setSelectedChild(savedChild);
          } else {
            // If saved child not found (maybe removed), select first child
            console.log('Saved child not found in current children list');
            if (childrenData.length > 0) {
              setSelectedChild(childrenData[0]);
            }
          }
        } else if (childrenData.length > 0 && !selectedChild) {
          // If no saved child, select the first one
          setSelectedChild(childrenData[0]);
        }
      } catch (error) {
        console.error('Error retrieving selected child from AsyncStorage:', error);
        // Fallback to selecting first child
        if (childrenData.length > 0 && !selectedChild) {
          setSelectedChild(childrenData[0]);
        }
      }

      // Fetch attendance data for all children
      if (childrenData.length > 0) {
        fetchAttendanceData(childrenData);
      }
    } catch (error) {
      console.error('Error fetching children:', error);
      Alert.alert('Error', 'Failed to load children data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const [upcomingEvents, setUpcomingEvents] = useState<Event[]>([]);
  const [todayEvents, setTodayEvents] = useState<Event[]>([]);
  const [eventsLoading, setEventsLoading] = useState(false);
  const [currentDate, setCurrentDate] = useState<string>('');

  // Fetch events from Firestore and update current date
  useEffect(() => {
    let isMounted = true;
    
    const initialize = async () => {
      try {
        // First update the current date
        const { today, todayStr, todayYear, todayMonth, todayDay } = await getCurrentDate();
        
        if (isMounted) {
          // Format the date for display
          const options: Intl.DateTimeFormatOptions = { 
            day: 'numeric', 
            month: 'short', 
            year: 'numeric' 
          };
          setCurrentDate(today.toLocaleDateString('en-IN', options));
          
          // Then fetch events
          if (user) {
            await fetchEvents(today, todayStr, todayYear, todayMonth, todayDay);
          }
        }
      } catch (error) {
        console.error('Error initializing:', error);
      }
    };
    
    initialize();
    
    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [user]);

  // Function to get current date in IST (India Standard Time)
  const getCurrentDate = (): {today: Date, todayStr: string, todayYear: number, todayMonth: number, todayDay: number} => {
    // Create a date object for the current moment in the local timezone
    const now = new Date();
    
    // Get the current timezone offset in minutes, then convert to hours
    const localOffset = now.getTimezoneOffset() / 60;
    
    // IST is UTC+5:30, so we need to adjust from local time to IST
    // Calculate the difference between local time and IST in hours
    const istOffset = 5.5; // IST is UTC+5:30
    const offsetDiff = istOffset + localOffset; // difference between local time and IST
    
    // Create a new date adjusted to IST
    const istNow = new Date(now.getTime() + (offsetDiff * 60 * 60 * 1000));
    
    // Set to beginning of day in IST
    const today = new Date(istNow);
    today.setHours(0, 0, 0, 0);
    
    // Get date components in IST
    const todayYear = today.getFullYear();
    const todayMonth = today.getMonth() + 1; // getMonth() is 0-indexed
    const todayDay = today.getDate();
    
    // Create a date string in YYYY-MM-DD format in IST
    const todayStr = `${todayYear}-${String(todayMonth).padStart(2, '0')}-${String(todayDay).padStart(2, '0')}`;
    
    console.log(`Current date in IST: ${todayStr} (${todayDay}/${todayMonth}/${todayYear})`);
    
    return {
      today,
      todayStr,
      todayYear,
      todayMonth,
      todayDay
    };
  };

  const fetchEvents = async (today: Date, todayStr: string, todayYear: number, todayMonth: number, todayDay: number): Promise<void> => {
    try {
      setEventsLoading(true);
      console.log('Fetching events from Firestore...');

      console.log(`Using date for events: ${todayStr} (${todayYear}-${todayMonth}-${todayDay})`);

      const eventsCollection = collection(firestore, 'events');
      const eventsSnapshot = await getDocs(eventsCollection);

      const eventsData: Event[] = [];

      eventsSnapshot.forEach((doc) => {
        const data = doc.data();
        console.log(`Processing event: ${data.title}, date: ${data.date}`);

        // Handle different date formats
        let eventDate: Date;
        let eventDateStr: string;
        let dateComponents: {year: number, month: number, day: number};

        if (typeof data.date === 'string') {
          // Handle string date
          eventDate = new Date(data.date);
          eventDateStr = data.date;
        } else if (data.date && typeof data.date === 'object' && 'toDate' in data.date) {
          // Handle Firestore timestamp
          eventDate = (data.date as any).toDate();
          eventDateStr = eventDate.toISOString().split('T')[0];
        } else {
          // Default to today if no valid date
          eventDate = new Date();
          eventDateStr = eventDate.toISOString().split('T')[0];
        }

        // Extract date components for comparison
        dateComponents = {
          year: eventDate.getFullYear(),
          month: eventDate.getMonth() + 1,
          day: eventDate.getDate()
        };

        console.log(`Event date components: ${dateComponents.year}-${dateComponents.month}-${dateComponents.day}`);

        // Check if this is today's event using multiple methods
        // 1. Using date components (most reliable)
        const isTodayByComponents = (
          dateComponents.year === todayYear &&
          dateComponents.month === todayMonth &&
          dateComponents.day === todayDay
        );

        // 2. Direct string comparison
        const isTodayByString = eventDateStr === todayStr;

        // 3. Try to extract just the date part if it's a full ISO string
        let isTodayByExtracted = false;
        if (eventDateStr && eventDateStr.includes('T')) {
          const extractedDate = eventDateStr.split('T')[0];
          isTodayByExtracted = extractedDate === todayStr;
        }

        const isToday = isTodayByComponents || isTodayByString || isTodayByExtracted;
        console.log(`Is today's event? ${isToday} (by components: ${isTodayByComponents}, by string: ${isTodayByString}, by extracted: ${isTodayByExtracted})`);

        // Skip past events but include today's events
        const isPast = (
          dateComponents.year < todayYear ||
          (dateComponents.year === todayYear && dateComponents.month < todayMonth) ||
          (dateComponents.year === todayYear && dateComponents.month === todayMonth && dateComponents.day < todayDay)
        );

        if (isPast && !isToday) {
          console.log(`Skipping past event: ${data.title}`);
          return;
        }

        // Include events that are for all or specifically for parents
        if (data.targetAudience === 'all' || data.targetAudience === 'parents' || !data.targetAudience) {
          eventsData.push({
            id: doc.id,
            title: data.title || 'Untitled Event',
            description: data.description || '',
            date: eventDateStr,
            type: data.type || 'other',
            targetAudience: data.targetAudience || 'all',
            isToday: isToday // Add a flag to easily identify today's events
          });
          console.log(`Added event: ${data.title}, isToday: ${isToday}`);
        }
      });

      // First, process all events to ensure consistent date handling
      const processedEvents = eventsData.map(event => {
        let eventDate: Date;
        let eventDateStr = event.date;
        
        // Parse the date
        if (typeof event.date === 'string') {
          eventDate = new Date(event.date);
        } else if (event.date && typeof event.date === 'object' && 'toDate' in event.date) {
          // Handle Firestore timestamp
          eventDate = (event.date as any).toDate();
          eventDateStr = eventDate.toISOString().split('T')[0];
        } else {
          eventDate = new Date();
          eventDateStr = eventDate.toISOString().split('T')[0];
        }
        
        // Get date components in local time (assuming the event date is already in the correct timezone)
        const eventYear = eventDate.getFullYear();
        const eventMonth = eventDate.getMonth() + 1;
        const eventDay = eventDate.getDate();
        
        // Check if this is today's event
        const isToday = (
          eventYear === todayYear &&
          eventMonth === todayMonth &&
          eventDay === todayDay
        );
        
        // Check if this is a past event
        const isPast = (
          eventYear < todayYear ||
          (eventYear === todayYear && eventMonth < todayMonth) ||
          (eventYear === todayYear && eventMonth === todayMonth && eventDay < todayDay)
        );
        
        // Format the date consistently
        const formattedDate = `${String(eventDay).padStart(2, '0')}/${String(eventMonth).padStart(2, '0')}/${eventYear}`;
        
        return {
          ...event,
          date: formattedDate,
          isToday,
          isPast,
          sortDate: new Date(eventYear, eventMonth - 1, eventDay).getTime()
        };
      });
      
      // Separate today's events from upcoming events
      const todaysEvents = processedEvents
        .filter(event => event.isToday)
        .sort((a, b) => a.sortDate - b.sortDate);
      
      // Get future events (not today's events and not past events)
      const futureEvents = processedEvents
        .filter(event => !event.isToday && !event.isPast)
        .sort((a, b) => a.sortDate - b.sortDate);
      
      // Log for debugging
      console.log(`Found ${todaysEvents.length} events for today:`);
      todaysEvents.forEach(event => {
        console.log(`- ${event.title} (${event.date})`);
      });
      
      console.log(`Found ${futureEvents.length} upcoming events:`);
      futureEvents.forEach(event => {
        console.log(`- ${event.title} (${event.date})`);
      });

      // Sort events by date (closest first)
      futureEvents.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      console.log(`Fetched ${eventsData.length} events (${todaysEvents.length} today, ${futureEvents.length} upcoming)`);

      // Log today's events for debugging
      if (todaysEvents.length > 0) {
        console.log('Today\'s events:');
        todaysEvents.forEach(event => {
          console.log(`- ${event.title} (${event.date})`);
        });
      } else {
        console.log('No events found for today');
        // Uncomment the following line to create a test event for today
        // await createTestEventForToday();
      }
      setTodayEvents(todaysEvents);
      setUpcomingEvents(futureEvents);
    } catch (error) {
      console.error('Error fetching events:', error);
      Alert.alert('Error', 'Failed to load events. Please try again.');
    } finally {
      setEventsLoading(false);
    }
  };

  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [announcementsLoading, setAnnouncementsLoading] = useState(false);

  // Fetch announcements from Firestore
  useEffect(() => {
    if (user) {
      fetchAnnouncements();
    }
  }, [user]);

  const fetchAnnouncements = async (): Promise<void> => {
    try {
      setAnnouncementsLoading(true);
      console.log('Fetching announcements from Firestore...');

      const announcementsCollection = collection(firestore, 'announcements');
      const announcementsSnapshot = await getDocs(announcementsCollection);

      const announcementsData: Announcement[] = [];
      announcementsSnapshot.forEach((doc) => {
        const data = doc.data();
        const targetAudience = data.targetAudience || 'all';

        // Only include announcements for parents or all users
        if (targetAudience === 'all' || targetAudience === 'parents') {
          announcementsData.push({
            id: doc.id,
            title: data.title || 'Untitled Announcement',
            message: data.message || '',
            date: data.date || new Date().toISOString().split('T')[0],
            targetAudience
          });
        }
      });

      // Sort announcements by date (newest first)
      announcementsData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      console.log(`Fetched ${announcementsData.length} announcements`);
      setAnnouncements(announcementsData);
    } catch (error) {
      console.error('Error fetching announcements:', error);
      Alert.alert('Error', 'Failed to load announcements. Please try again.');
    } finally {
      setAnnouncementsLoading(false);
    }
  };

  const handleSelectChild = async (child: Child) => {
    setSelectedChild(child);
    setShowChildModal(false);

    // Save selected child to AsyncStorage
    try {
      await AsyncStorage.setItem('selectedChildId', child.id);
      console.log(`Saved selected child ID ${child.id} to AsyncStorage`);
    } catch (error) {
      console.error('Error saving selected child to AsyncStorage:', error);
    }
  };

  const screenWidth = Dimensions.get('window').width;

  // Fetch attendance data for all children
  const fetchAttendanceData = async (childrenList: Child[]): Promise<void> => {
    try {
      console.log('Fetching attendance data for all children...');
      const attendanceCollection = collection(firestore, 'attendance');
      const attendanceSnapshot = await getDocs(attendanceCollection);

      // Initialize attendance data for each child
      const newAttendanceData: {[studentId: string]: {present: number, total: number}} = {};
      childrenList.forEach(child => {
        newAttendanceData[child.id] = { present: 0, total: 0 };
      });

      // Process each attendance document
      attendanceSnapshot.forEach((doc) => {
        const data = doc.data();
        const students = data.students || {};

        // For each child, check if they are in this attendance record
        childrenList.forEach(child => {
          // Only process documents for the child's class
          // Document ID format is: {className}_{date}
          if (doc.id.startsWith(`${child.class}_`)) {
            if (child.id in students) {
              // Increment total for this child
              newAttendanceData[child.id].total++;

              // If present, increment present count
              if (students[child.id] === 'present') {
                newAttendanceData[child.id].present++;
              }
            }
          }
        });
      });

      console.log('Attendance data:', newAttendanceData);
      setAttendanceData(newAttendanceData);
    } catch (error) {
      console.error('Error fetching attendance data:', error);
    }
  };

  // Calculate attendance percentage
  const getAttendancePercentage = (childId: string) => {
    const data = attendanceData[childId];
    if (!data || data.total === 0) return '0';
    return ((data.present / data.total) * 100).toFixed(0);
  };

  const attendancePercentage = selectedChild ? getAttendancePercentage(selectedChild.id) : '0';

  // Quick actions
  const quickActions = [
    { id: '1', title: 'Pay Fees', icon: 'cash', color: '#4CAF50', route: '/payment' },
    { id: '2', title: 'Attendance', icon: 'calendar-check', color: '#2196F3', route: '/attendance' },
    { id: '3', title: 'Calendar', icon: 'calendar', color: '#FF9800', route: '/calendar' },
    { id: '4', title: 'Contact', icon: 'phone', color: '#9C27B0', route: '/contact' },
  ];

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Status Bar */}
      <StatusBar
        barStyle="light-content"
        backgroundColor="#3B82F6"
        translucent={true}
      />

      {/* Loading Indicator - Only show when not refreshing */}
      {isLoading && !refreshing && (
        <View className="absolute z-10 w-full h-full bg-black/30 items-center justify-center">
          <View className="bg-white p-4 rounded-xl shadow-lg">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="text-gray-700 mt-2 font-medium">Loading...</Text>
          </View>
        </View>
      )}

      {/* Header with Colorful Background */}
      <View
        className="pb-8 px-5 rounded-b-3xl shadow-lg"
        style={{
          paddingTop: Platform.OS === 'android' ? 45 : 25,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.2,
          shadowRadius: 8,
          elevation: 10,
          backgroundColor: '#3B82F6', // Blue color to match teacher theme
          borderBottomWidth: 0,
          borderLeftWidth: 0,
          borderRightWidth: 0
        }}
      >
        <View className="flex-row justify-between items-center mb-5">
          <View className="flex-row items-center">
            <View className="w-14 h-14 bg-white rounded-lg items-center justify-center mr-4 shadow-md overflow-hidden" style={{ elevation: 5, borderWidth: 2, borderColor: '#3B82F6' }}>
              <Image
                source={require('../../assets/logo2.jpeg')}
                style={{ width: '90%', height: '90%' }}
                resizeMode="contain"
              />
            </View>
            <View>
              <Text className="text-white text-4xl font-bold">NUTKHUT</Text>
              <View className="h-1 w-20 bg-white/40 rounded-full mt-1"></View>
            </View>
          </View>
          <TouchableOpacity
            className="w-14 h-14 rounded-lg items-center justify-center shadow-lg"
            onPress={() => router.push('/profile')}
            style={{
              elevation: 5,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.2,
              shadowRadius: 4,
              backgroundColor: '#3B82F6', // Blue background to match teacher theme
              borderWidth: 0,
            }}
          >
            <FontAwesome name="user" size={26} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        <View className="mb-4 pl-1">
          <Text style={{ color: '#E3F2FD', fontSize: 16, fontWeight: '500', letterSpacing: 0.5 }}>Welcome,</Text>
          <Text style={{ color: 'white', fontSize: 22, fontWeight: 'bold', textShadowColor: 'rgba(0,0,0,0.2)', textShadowOffset: {width: 1, height: 1}, textShadowRadius: 2 }}>
            {user?.name ? user.name.split(' ').map(name => name.charAt(0).toUpperCase() + name.slice(1).toLowerCase()).join(' ') : 'Parent'}{' 👋'}
          </Text>
        </View>

        {/* Child Selector */}
        <TouchableOpacity
          className="flex-row items-center p-4 rounded-xl shadow-md"
          onPress={() => setShowChildModal(true)}
          style={{
            backgroundColor: '#FFFFFF',  // White background
            borderWidth: 1,
            borderColor: '#BBDEFB',  // Light blue border to match theme
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 0.2,
            shadowRadius: 5,
            elevation: 5
          }}
        >
          <View className="w-12 h-12 bg-blue-50 rounded-lg items-center justify-center mr-4 shadow-sm">
            <FontAwesome name="child" size={22} color="#3B82F6" />
          </View>
          <View className="flex-1">
            <View className="flex-row items-center">
              <Text className="text-gray-800 font-bold text-lg mr-2">
                {selectedChild ? selectedChild.name : 'Select Child'}
              </Text>
              <FontAwesome name="chevron-down" size={14} color="#3B82F6" />
            </View>
            {selectedChild && (
              <View className="flex-row items-center mt-1">
                <Text className="text-gray-600 font-medium text-sm mr-1">Class</Text>
                <View className="bg-blue-100 px-2 py-1 rounded-md">
                  <Text className="text-blue-800 font-bold text-sm">{selectedChild.class}</Text>
                </View>
              </View>
            )}
          </View>
        </TouchableOpacity>
      </View>

      {/* Main Content */}
      <ScrollView
        className="flex-1 px-4 pt-4"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#3B82F6']}
            tintColor="#3B82F6"
          />
        }
      >
        {/* Quick Actions */}
        <View className="mb-6">
          <Text className="text-gray-800 text-lg font-bold mb-3 px-1">Quick Actions</Text>
          <View className="flex-row flex-wrap justify-between">
            {quickActions.map((action) => (
              <TouchableOpacity
                key={action.id}
                className="bg-white w-[48%] p-4 rounded-xl shadow-sm mb-3 border border-gray-100"
                onPress={() => router.push(action.route as any)}
              >
                <View style={{ backgroundColor: `${action.color}20` }} className="w-12 h-12 rounded-full items-center justify-center mb-2">
                  <MaterialCommunityIcons name={action.icon as any} size={24} color={action.color} />
                </View>
                <Text className="font-semibold text-gray-800">{action.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Attendance Summary */}
        {selectedChild && (
          <View className="bg-white p-4 rounded-xl shadow-sm mb-6 border border-gray-100">
            <View className="flex-row justify-between items-center mb-3">
              <Text className="text-gray-800 font-bold text-base">Attendance</Text>
              <TouchableOpacity onPress={() => router.push('/attendance')}>
                <Text className="text-blue-500 font-medium">View Details</Text>
              </TouchableOpacity>
            </View>

            <View className="flex-row items-center">
              {/* Attendance percentage indicator with dual colors */}
              <View className="w-20 h-20 mr-4 bg-gray-100 rounded-lg justify-center items-center overflow-hidden">
                {/* Red background for absent */}
                <View className="absolute bottom-0 left-0 right-0 bg-red-500 h-full" />

                {/* Green overlay for present percentage */}
                <View
                  className="absolute bottom-0 left-0 right-0 bg-green-500"
                  style={{ height: `${attendancePercentage}%` }}
                />

                {/* Percentage text */}
                <View className="absolute inset-0 justify-center items-center">
                  <Text
                    className="text-2xl font-bold text-white"
                    style={{
                      textShadowColor: 'rgba(0, 0, 0, 0.5)',
                      textShadowOffset: { width: 1, height: 1 },
                      textShadowRadius: 2
                    }}
                  >
                    {attendancePercentage}%
                  </Text>
                </View>
              </View>

              <View className="flex-1">
                <View className="flex-row justify-between mb-1">
                  <Text className="text-gray-600">Present</Text>
                  <Text className="font-medium">{attendanceData[selectedChild.id]?.present || 0} days</Text>
                </View>
                <View className="flex-row justify-between mb-1">
                  <Text className="text-gray-600">Absent</Text>
                  <Text className="font-medium">{(attendanceData[selectedChild.id]?.total || 0) - (attendanceData[selectedChild.id]?.present || 0)} days</Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-gray-600">Total</Text>
                  <Text className="font-medium">{attendanceData[selectedChild.id]?.total || 0} days</Text>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Today's Events */}
        <View className="mb-6">
          <View className="flex-row justify-between items-center mb-4">
            <View className="flex-row items-center">
              <View className="w-8 h-8 bg-blue-500 rounded-full items-center justify-center mr-2">
                <FontAwesome name="calendar-check-o" size={16} color="white" />
              </View>
              <Text className="text-gray-800 text-lg font-bold">Today's Events</Text>
            </View>
            <View className="bg-blue-500 px-3 py-1 rounded-full shadow-sm">
              <Text className="text-xs text-white font-medium">
                {currentDate || new Date().toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' })}
              </Text>
            </View>
          </View>

          {eventsLoading ? (
            <View className="bg-white p-6 rounded-xl shadow-sm mb-3 border border-gray-100 items-center justify-center">
              <ActivityIndicator size="small" color="#3B82F6" />
              <Text className="text-gray-500 mt-2">Loading events...</Text>
            </View>
          ) : todayEvents.length > 0 ? (
            <View className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100">
              {todayEvents.map((event, index) => (
                <View key={`today-${event.id}`}>
                  {index > 0 && <View className="h-px bg-gray-100 mx-4" />}
                  <View
                    className="p-4"
                    style={{
                      backgroundColor: event.type === 'holiday' ? '#FEF2F2' :
                                      event.type === 'exam' ? '#FFFBEB' :
                                      event.type === 'activity' ? '#ECFDF5' : 'white'
                    }}
                  >
                    <View className="flex-row items-center mb-2">
                      <View
                        className="w-10 h-10 rounded-full items-center justify-center mr-3"
                        style={{
                          backgroundColor: event.type === 'holiday' ? '#FEE2E2' :
                                          event.type === 'exam' ? '#FEF3C7' :
                                          event.type === 'activity' ? '#D1FAE5' : '#F3F4F6'
                        }}
                      >
                        <FontAwesome
                          name={event.type === 'holiday' ? 'calendar' :
                                event.type === 'exam' ? 'pencil' :
                                event.type === 'activity' ? 'star' : 'circle'}
                          size={18}
                          color={event.type === 'holiday' ? '#EF4444' :
                                event.type === 'exam' ? '#F59E0B' :
                                event.type === 'activity' ? '#10B981' : '#6B7280'}
                        />
                      </View>

                      <View className="flex-1">
                        <Text className="font-bold text-gray-800 text-base">{event.title}</Text>
                        <View className="flex-row items-center mt-1">
                          <View
                            className="px-2 py-0.5 rounded-full mr-2"
                            style={{
                              backgroundColor: event.type === 'holiday' ? '#FEE2E2' :
                                              event.type === 'exam' ? '#FEF3C7' :
                                              event.type === 'activity' ? '#D1FAE5' : '#F3F4F6'
                            }}
                          >
                            <Text
                              className="text-xs font-medium capitalize"
                              style={{
                                color: event.type === 'holiday' ? '#EF4444' :
                                       event.type === 'exam' ? '#F59E0B' :
                                       event.type === 'activity' ? '#10B981' : '#6B7280'
                              }}
                            >
                              {event.type}
                            </Text>
                          </View>
                          <View className="flex-row items-center">
                            <FontAwesome name="clock-o" size={12} color="#3B82F6" style={{ marginRight: 4 }} />
                            <Text className="text-xs text-blue-500 font-medium">Today</Text>
                          </View>
                        </View>
                      </View>
                    </View>
                    {event.description && (
                      <Text className="text-gray-600 text-sm ml-13 pl-0.5">
                        {event.description.length > 100 ? `${event.description.substring(0, 100)}...` : event.description}
                      </Text>
                    )}
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View className="bg-white p-6 rounded-xl shadow-sm mb-3 border border-gray-100 items-center justify-center">
              <View className="w-16 h-16 bg-gray-100 rounded-full items-center justify-center mb-2">
                <FontAwesome name="calendar-check-o" size={28} color="#9CA3AF" />
              </View>
              <Text className="text-gray-700 font-medium">No events today</Text>
              <Text className="text-gray-500 text-sm text-center mt-1">Check back later for updates</Text>
            </View>
          )}
        </View>

        {/* Separator */}
        <View className="h-px bg-gray-200 my-2 mx-1" />

        {/* Upcoming Events */}
        <View className="mb-6">
          <View className="flex-row justify-between items-center mb-4">
            <View className="flex-row items-center">
              <View className="w-8 h-8 bg-indigo-500 rounded-full items-center justify-center mr-2">
                <FontAwesome name="calendar" size={16} color="white" />
              </View>
              <Text className="text-gray-800 text-lg font-bold">Upcoming Events</Text>
            </View>
            <TouchableOpacity
              className="bg-indigo-100 px-3 py-1 rounded-full flex-row items-center"
              onPress={() => router.push('/calendar')}
            >
              <Text className="text-indigo-700 font-medium mr-1">View All</Text>
              <FontAwesome name="angle-right" size={14} color="#4338CA" />
            </TouchableOpacity>
          </View>

          {eventsLoading ? (
            <View className="bg-white p-6 rounded-xl shadow-sm mb-3 border border-gray-100 items-center justify-center">
              <ActivityIndicator size="small" color="#4F46E5" />
              <Text className="text-gray-500 mt-2">Loading events...</Text>
            </View>
          ) : upcomingEvents.length > 0 ? (
            <View className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100">
              {upcomingEvents.slice(0, 3).map((event, index) => (
                <View key={event.id}>
                  {index > 0 && <View className="h-px bg-gray-100 mx-4" />}
                  <View className="p-4">
                    <View className="flex-row items-center">
                      <View
                        className="w-14 h-14 rounded-lg items-center justify-center mr-3 shadow-sm"
                        style={{
                          backgroundColor: event.type === 'holiday' ? '#FEE2E2' :
                                          event.type === 'exam' ? '#FEE2E2' :
                                          event.type === 'activity' ? '#D1FAE5' : '#F3F4F6',
                          borderWidth: 1,
                          borderColor: event.type === 'holiday' ? '#FECACA' :
                                     event.type === 'exam' ? '#FECACA' :
                                     event.type === 'activity' ? '#A7F3D0' : '#E5E7EB'
                        }}
                      >
                        <Text
                          className="text-xl font-bold"
                          style={{
                            color: event.type === 'holiday' ? '#DC2626' :
                                  event.type === 'exam' ? '#B91C1C' :
                                  event.type === 'activity' ? '#059669' : '#4F46E5',
                            lineHeight: 24
                          }}
                        >
                          {event.date.split('/')[0]}
                        </Text>
                        <Text
                          className="text-xs font-semibold mt-[-2px]"
                          style={{
                            color: event.type === 'holiday' ? '#DC2626' :
                                  event.type === 'exam' ? '#B91C1C' :
                                  event.type === 'activity' ? '#059669' : '#4F46E5',
                            textTransform: 'uppercase',
                            letterSpacing: 0.5
                          }}
                        >
                          {new Date(parseInt(event.date.split('/')[2]), parseInt(event.date.split('/')[1]) - 1, parseInt(event.date.split('/')[0])).toLocaleString('en-US', { month: 'short' })}
                        </Text>
                      </View>

                      <View className="flex-1 ml-3">
                        <Text className="font-semibold text-gray-900 text-base">{event.title}</Text>
                        <View className="flex-row items-center mt-1 flex-wrap">
                          <View
                            className="px-2.5 py-0.5 rounded-md mr-2 mb-1"
                            style={{
                              backgroundColor: event.type === 'holiday' ? '#FEE2E2' :
                                              event.type === 'exam' ? '#FEE2E2' :
                                              event.type === 'activity' ? '#D1FAE5' : '#F3F4F6',
                              borderWidth: 1,
                              borderColor: event.type === 'holiday' ? '#FECACA' :
                                         event.type === 'exam' ? '#FECACA' :
                                         event.type === 'activity' ? '#A7F3D0' : '#E5E7EB'
                            }}
                          >
                            <Text
                              className="text-xs font-semibold capitalize"
                              style={{
                                color: event.type === 'holiday' ? '#B91C1C' :
                                       event.type === 'exam' ? '#B91C1C' :
                                       event.type === 'activity' ? '#059669' : '#4B5563',
                                letterSpacing: 0.3
                              }}
                            >
                              {event.type}
                            </Text>
                          </View>
                          <View className="flex-row items-center">
                            <FontAwesome 
                              name="calendar" 
                              size={12} 
                              color={
                                event.type === 'holiday' ? '#B91C1C' :
                                event.type === 'exam' ? '#B91C1C' :
                                event.type === 'activity' ? '#059669' : '#4B5563'
                              } 
                              style={{ marginRight: 4 }} 
                            />
                            <Text 
                              className="text-xs font-medium"
                              style={{
                                color: event.type === 'holiday' ? '#B91C1C' :
                                       event.type === 'exam' ? '#B91C1C' :
                                       event.type === 'activity' ? '#059669' : '#4B5563',
                                fontFamily: 'Inter_500Medium'
                              }}
                            >
                              {new Date(
                                parseInt(event.date.split('/')[2]), 
                                parseInt(event.date.split('/')[1]) - 1, 
                                parseInt(event.date.split('/')[0])
                              ).toLocaleDateString('en-IN', {
                                day: 'numeric',
                                month: 'short',
                                year: 'numeric'
                              })}
                            </Text>
                          </View>
                        </View>
                        {event.description && (
                          <Text className="text-gray-600 text-xs mt-1" numberOfLines={2}>
                            {event.description}
                          </Text>
                        )}
                      </View>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View className="bg-white p-6 rounded-xl shadow-sm mb-3 border border-gray-100 items-center justify-center">
              <MaterialIcons name="event-busy" size={24} color="#9CA3AF" />
              <Text className="text-gray-500 mt-2">No upcoming events</Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Child Selection Modal */}
      <Modal
        visible={showChildModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowChildModal(false)}
      >
        <View className="flex-1 bg-black/60 justify-end">
          <View className="bg-white rounded-t-3xl p-6" style={{ elevation: 24, shadowColor: '#000', shadowOffset: { width: 0, height: -5 }, shadowOpacity: 0.3, shadowRadius: 10 }}>
            <View className="w-16 h-1 bg-gray-300 rounded-full self-center mb-6" />

            <View className="flex-row justify-between items-center mb-6">
              <View>
                <Text className="text-2xl font-bold text-gray-800">Select Child</Text>
                <Text className="text-gray-500 text-sm mt-1">Choose a child to view their information</Text>
              </View>
              <TouchableOpacity
                onPress={() => setShowChildModal(false)}
                className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center"
              >
                <FontAwesome name="close" size={18} color="#64748b" />
              </TouchableOpacity>
            </View>

            <View className="mb-6">
              {children.length > 0 ? (
                children.map((child) => (
                  <TouchableOpacity
                    key={child.id}
                    className={`flex-row items-center p-4 mb-3 rounded-xl ${selectedChild?.id === child.id ? 'bg-blue-50 border border-blue-200' : 'bg-white border border-gray-100'}`}
                    onPress={() => handleSelectChild(child)}
                    style={{
                      shadowColor: '#000',
                      shadowOffset: { width: 0, height: 1 },
                      shadowOpacity: 0.1,
                      shadowRadius: 2,
                      elevation: 2
                    }}
                  >
                    <View className={`w-14 h-14 rounded-full items-center justify-center mr-4 ${selectedChild?.id === child.id ? 'bg-blue-100' : 'bg-gray-100'}`}>
                      <FontAwesome name="child" size={24} color={selectedChild?.id === child.id ? '#3B82F6' : '#64748b'} />
                    </View>
                    <View className="flex-1">
                      <Text className={`text-lg font-bold ${selectedChild?.id === child.id ? 'text-blue-700' : 'text-gray-800'}`}>{child.name}</Text>
                      <View className="flex-row items-center mt-1">
                        <Text className={`text-sm ${selectedChild?.id === child.id ? 'text-blue-600' : 'text-gray-500'}`}>Class</Text>
                        <View className={`ml-2 px-3 py-1 rounded-full ${selectedChild?.id === child.id ? 'bg-blue-100' : 'bg-gray-100'}`}>
                          <Text className={`text-sm font-medium ${selectedChild?.id === child.id ? 'text-blue-700' : 'text-gray-700'}`}>{child.class}</Text>
                        </View>
                      </View>
                    </View>
                    {selectedChild?.id === child.id && (
                      <View className="w-8 h-8 bg-blue-500 rounded-full items-center justify-center">
                        <FontAwesome name="check" size={14} color="white" />
                      </View>
                    )}
                  </TouchableOpacity>
                ))
              ) : (
                <View className="p-6 border border-gray-200 rounded-xl bg-gray-50 items-center">
                  <View className="w-16 h-16 bg-gray-200 rounded-full items-center justify-center mb-3">
                    <FontAwesome name="child" size={28} color="#9CA3AF" />
                  </View>
                  <Text className="text-center text-gray-700 font-medium mb-1">No children found</Text>
                  <Text className="text-center text-gray-500 text-sm">
                    Please update your phone number in profile to match the one used when registering your children.
                  </Text>
                </View>
              )}
            </View>

            <TouchableOpacity
              className="bg-blue-500 p-4 rounded-xl mb-6 shadow-md"
              onPress={() => setShowChildModal(false)}
              style={{
                shadowColor: '#3B82F6',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.3,
                shadowRadius: 4,
                elevation: 3
              }}
            >
              <Text className="text-white text-center font-bold text-lg">Done</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}